<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::create('student_id_card_format', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('college_id');
            $table->string('card_template', 50)->default('churchill');
            $table->string('card_orientation', 20)->default('landscape');
            $table->string('primary_color', 7)->default('#0161a3');
            $table->string('secondary_color', 7)->default('#ffffff');
            $table->string('logo_position', 20)->default('top-left');
            $table->boolean('show_expiry_date')->default(true);
            $table->integer('expiry_years')->default(2);
            $table->string('background_image')->nullable();
            $table->text('custom_css')->nullable();
            $table->json('field_settings')->nullable();
            $table->timestamps();

            $table->index('college_id');
            $table->unique('college_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('student_id_card_format');
    }

};