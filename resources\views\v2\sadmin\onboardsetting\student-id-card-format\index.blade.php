<x-v2.layouts.onboard>

    @section('title', $title)
    @section('keywords', $keywords)
    @section('description', $description)
    @section('mainmenu', $mainmenu)

    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/sadmin/onboard.css') }}">
    </x-slot>

    <style>
        input[type="file" i] {
            appearance: none;
            background-color: initial;
            cursor: default;
            align-items: baseline;
            color: inherit;
            text-overflow: ellipsis;
            white-space: pre;
            text-align: start !important;
            padding: initial;
            border: initial;
            overflow: hidden !important;
        }

        #studentIdCardSettingForm .k-form-field {
            margin-bottom: 20px;
        }

        .color-preview {
            width: 30px;
            height: 30px;
            border: 1px solid #ccc;
            border-radius: 4px;
            display: inline-block;
            margin-left: 10px;
            vertical-align: middle;
        }
    </style>

    <div class="flex h-full flex-row p-6">
        <div class="w-full bg-white rounded-lg p-6 shadow">
            <input id="college_id" type="hidden" name="college_id" value="{{ isset($college_id) ? $college_id : '' }}" />
            <livewire:onboarding.progress form="templates_letters.student_id_card_format" />
            <div class="h-full w-full rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <div class="w-full">
                    <form id="studentIdCardSettingForm">
                        <input type="hidden" name="tracking_form" value="templates_letters.student_id_card_format" />
                    </form>
                </div>
            </div>
        </div>
    </div>

    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/sadmin/student-id-card-setting.js') }}"></script>
        <script src="{{ asset('plugins/ckeditor5/ckeditor.js') }}"></script>
    </x-slot>

    <x-slot name="fixVariables">
        var api_token = "{{ isset($api_token) ? "Bearer  $api_token" : '' }}"
    </x-slot>
</x-v2.layouts.onboard>
