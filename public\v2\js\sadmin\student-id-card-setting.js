$(document).ready(function () {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
    });

    $('#loader').kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 90px; height: 90px;"></div>',
    };

    $('#studentIdCardSettingForm').kendoForm({
        orientation: 'vertical',
        type: 'group',
        formData: {
            tracking_form: 'templates_letters.student_id_card_setting',
        },
        items: [
            {
                field: 'card_template',
                label: 'Card Template',
                editor: function (container, options) {
                    $(
                        "<select class='k-dropdown' id='card_template' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "'>" +
                            "<option value='churchill'>Churchill Institute Template</option>" +
                            "<option value='default'>Default Template</option>" +
                            "<option value='custom'>Custom Template</option>" +
                            "</select>"
                    ).appendTo(container);
                },
            },
            {
                field: 'card_orientation',
                label: 'Card Orientation',
                editor: function (container, options) {
                    $(
                        "<select class='k-dropdown' id='card_orientation' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "'>" +
                            "<option value='landscape'>Landscape (3.375\" x 2.125\")</option>" +
                            "<option value='portrait'>Portrait (2.125\" x 3.375\")</option>" +
                            "</select>"
                    ).appendTo(container);
                },
            },
            {
                field: 'primary_color',
                label: 'Primary Color',
                editor: function (container, options) {
                    $(
                        "<input class='k-textbox' id='primary_color' type='color' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "' value='#0161a3' />"
                    ).appendTo(container);
                },
            },
            {
                field: 'secondary_color',
                label: 'Secondary Color',
                editor: function (container, options) {
                    $(
                        "<input class='k-textbox' id='secondary_color' type='color' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "' value='#ffffff' />"
                    ).appendTo(container);
                },
            },
            {
                field: 'logo_position',
                label: 'Logo Position',
                editor: function (container, options) {
                    $(
                        "<select class='k-dropdown' id='logo_position' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "'>" +
                            "<option value='top-left'>Top Left</option>" +
                            "<option value='top-right'>Top Right</option>" +
                            "<option value='top-center'>Top Center</option>" +
                            "<option value='bottom-left'>Bottom Left</option>" +
                            "<option value='bottom-right'>Bottom Right</option>" +
                            "</select>"
                    ).appendTo(container);
                },
            },
            {
                field: 'show_expiry_date',
                label: 'Show Expiry Date',
                editor: function (container, options) {
                    $(
                        "<input type='checkbox' id='show_expiry_date' name='" +
                            options.field +
                            "' data-bind='checked: " +
                            options.field +
                            "' />"
                    ).appendTo(container);
                },
            },
            {
                field: 'expiry_years',
                label: 'Card Validity (Years)',
                editor: function (container, options) {
                    $(
                        "<input class='k-textbox' id='expiry_years' type='number' min='1' max='10' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "' value='2' />"
                    ).appendTo(container);
                },
            },
            {
                field: 'background_image',
                label: 'Background Image',
                editor: function (container, options) {
                    $(
                        "<input aria-labelledby='background_image-form-label' class='k-textbox k-valid' id='background_image' type='file' accept='image/png, image/jpeg' name='" +
                            options.field +
                            "' />"
                    ).appendTo(container);
                },
            },
        ],
        buttonsTemplate:
            '<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<button type="button" class="previewCardBtn flex justify-center h-9 px-3 py-2 bg-gray-500 shadow-none hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-600">\n' +
            '<p class="text-sm text-white">Preview Card</p>\n' +
            '</button>\n' +
            '<button type="submit" class="flex justify-center h-9 px-3 py-2 bg-primary-blue-500 shadow-none hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm text-white">Update Card Settings</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
        submit: function (ev) {
            saveModalData('studentIdCardSettingForm', 'student_id_card_setting');
            ev.preventDefault();
            return false;
        },
    });

    // Initialize dropdowns
    $('#card_template').kendoDropDownList({
        value: 'churchill'
    });
    
    $('#card_orientation').kendoDropDownList({
        value: 'landscape'
    });
    
    $('#logo_position').kendoDropDownList({
        value: 'top-left'
    });

    // Initialize file upload for background image
    $('#background_image').kendoUpload({
        async: {
            chunkSize: 20000000, // bytes
            saveUrl: site_url + 'api/upload-student-card-background',
            autoUpload: true,
        },
        multiple: false,
        validation: {
            allowedExtensions: ['.jpg', '.png'],
            maxFileSize: 20000000,
        },
        select: function (e) {
            const allowedExtensions = ['.jpg', '.png'];
            let isValid = true;

            for (let i = 0; i < e.files.length; i++) {
                let ext = e.files[i].extension.toLowerCase();
                if (!allowedExtensions.includes(ext)) {
                    isValid = false;
                    break;
                }
            }

            if (!isValid) {
                e.preventDefault();
                notificationDisplay(
                    'File type not allowed.(Only PNG and JPG allowed.)',
                    '',
                    'error'
                );
            }
        },
        success: function (e) {
            if (e.operation == 'upload') {
                notificationDisplay(
                    'Background image uploaded successfully.',
                    '',
                    'success'
                );
            }
        },
        error: function (e) {
            notificationDisplay('Something went wrong. Please try again.', '', 'error');
        },
    });

    // Load existing settings
    let collegeID = $('#college_id').val();
    $.ajax({
        type: 'POST',
        url: site_url + 'api/get-student-card-settings',
        dataType: 'json',
        data: { college_id: collegeID },
        success: function (response) {
            if (response.status == 'success' && response.data) {
                // Populate form with existing settings
                $('#card_template').data('kendoDropDownList').value(response.data.card_template || 'churchill');
                $('#card_orientation').data('kendoDropDownList').value(response.data.card_orientation || 'landscape');
                $('#logo_position').data('kendoDropDownList').value(response.data.logo_position || 'top-left');
                $('#primary_color').val(response.data.primary_color || '#0161a3');
                $('#secondary_color').val(response.data.secondary_color || '#ffffff');
                $('#show_expiry_date').prop('checked', response.data.show_expiry_date == '1');
                $('#expiry_years').val(response.data.expiry_years || '2');
            }
        },
        error: function() {
            // Set default values if no settings found
            $('#card_template').data('kendoDropDownList').value('churchill');
            $('#card_orientation').data('kendoDropDownList').value('landscape');
            $('#logo_position').data('kendoDropDownList').value('top-left');
        }
    });

    function saveModalData(modalId = '', type = '') {
        if (modalId.length > 0) {
            let dataArr = {};
            let serializeArr = $(document)
                .find('#' + modalId)
                .find('input[name], select[name], textarea[name]')
                .serializeArray();
            $(serializeArr).each(function (i, field) {
                dataArr[field.name] = field.value;
            });
            
            // Handle checkbox values
            dataArr['show_expiry_date'] = $('#show_expiry_date').is(':checked') ? '1' : '0';
            
            console.log(dataArr);
            $.ajax({
                type: 'POST',
                url: site_url + 'api/onboard-setup/ajaxAction',
                dataType: 'json',
                data: { action: type, data: dataArr },
                success: function (response) {
                    notificationDisplay(response.message, '', response.status);
                },
            });
        }
    }

    // Preview card functionality
    $('body').on('click', '.previewCardBtn', function () {
        let collegeID = $('#college_id').val();
        let settings = {
            card_template: $('#card_template').data('kendoDropDownList').value(),
            card_orientation: $('#card_orientation').data('kendoDropDownList').value(),
            logo_position: $('#logo_position').data('kendoDropDownList').value(),
            primary_color: $('#primary_color').val(),
            secondary_color: $('#secondary_color').val(),
            show_expiry_date: $('#show_expiry_date').is(':checked') ? '1' : '0',
            expiry_years: $('#expiry_years').val()
        };
        
        $.ajax({
            type: 'POST',
            url: site_url + 'api/preview-student-card',
            dataType: 'json',
            data: { 
                college_id: collegeID,
                settings: settings
            },
            success: function (response) {
                if (response.status == 'success') {
                    // Open preview in new window
                    window.open(response.preview_url, '_blank');
                } else {
                    notificationDisplay(response.message, '', 'error');
                }
            },
            error: function() {
                notificationDisplay('Failed to generate preview.', '', 'error');
            }
        });
    });
});
