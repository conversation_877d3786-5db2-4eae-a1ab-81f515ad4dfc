var tcsiCreditOfferGridId = '#tcsiCreditOfferList';
var tcsiCreditOfferFlag = true;
var tcsiStudentCourseList = [];
var singleTimeLoadFlag = true;

var addTcsiCreditOfferModalId = '#addTcsiCreditOfferModal';
var editTcsiCreditOfferModalId = '#editTcsiCreditOfferModal';
var deleteTcsiCreditOfferModalId = '#deleteTcsiCreditOfferModal';

$('#viewSanctionModal').kendoWindow(defaultWindowSlideFormat('Sanction', 80));
$('#addSanctionModal').kendoWindow(defaultWindowSlideFormat('Sanction Details', 65));
$('#editSanctionModal').kendoWindow(defaultWindowSlideFormat('Sanction Details', 65));
$('#viewExitInterviewModal').kendoWindow(defaultWindowSlideFormat('Exit Interview', 80));
$('#addExitInterviewModal').kendoWindow(
    defaultWindowSlideFormat('Add Student Exit Interview Details', 65)
);
$('#editExitInterviewModal').kendoWindow(
    defaultWindowSlideFormat('Edit Student Exit Interview Details', 65)
);
$('#viewStudentTCSIModal').kendoWindow(defaultWindowSlideFormat('TCSI', 80));

$('#deleteStudentSanctionModal').kendoDialog({
    width: '400px',
    title: 'Delete',
    content:
        "Are you sure you want to delete this student sanction? <input type='hidden' name='id' id='deleteStudentSanctionId' />",
    actions: [
        { text: 'Close' },
        {
            text: 'Yes',
            primary: true,
            action: function () {
                deleteStudentSanction(
                    $('#deleteStudentSanctionModal').find('#deleteStudentSanctionId').val()
                );
            },
        },
    ],
    animation: defaultOpenAnimation(),
    open: onOpenDeleteDialog('#deleteStudentSanctionModal'),
    visible: false,
});
$('#deleteStudentExitInterviewModal').kendoDialog({
    width: '400px',
    title: 'Delete',
    content:
        "Are you sure you want to delete this student exit interview? <input type='hidden' name='id' id='deleteStudentExitInterviewId' />",
    actions: [
        { text: 'Close' },
        {
            text: 'Yes',
            primary: true,
            action: function () {
                deleteStudentExitInterview(
                    $('#deleteStudentExitInterviewModal')
                        .find('#deleteStudentExitInterviewId')
                        .val()
                );
            },
        },
    ],
    animation: defaultOpenAnimation(),
    open: onOpenDeleteDialog('#deleteStudentExitInterviewModal'),
    visible: false,
});
$('#resetPasswordModal').kendoDialog({
    width: '500px',
    title: 'Reset Password',
    content:
        "<div class='mb-2'><span class='text-gray-700 text-base font-normal leading-normal tracking-tight'>Do you want to reset password for </span><span class='studentName text-blue-500 text-base font-normal leading-normal tracking-tight studentName' >Rosamaria Mel</span></div><div class='text-gray-500 text-sm font-normal leading-5 tracking-wide'>Clicking this Yes allows you to enter an email or phone number. Information for unlocking your account will then be sent to you via SMS or email, based on your choice.</div><input type='hidden' name='id' id='deleteStudentExitInterviewId' />",
    actions: [
        { text: 'Close' },
        {
            text: 'Yes',
            primary: true,
            action: function () {
                sendResetPasswordLink();
            },
        },
    ],
    animation: defaultOpenAnimation(),
    open: onOpenDeleteDialog('#resetPasswordModal'),
    visible: false,
});
$('#reSendActivationModal').kendoDialog({
    width: '500px',
    title: 'Resend Activation',
    content:
        "<div class='mb-2'><span class='text-gray-700 text-base font-normal leading-normal tracking-tight'>Do you want to Resend Activation for </span><span class='studentName text-blue-500 text-base font-normal leading-normal tracking-tight studentName' >Rosamaria Mel</span></div><div class='text-gray-500 text-sm font-normal leading-5 tracking-wide'>Clicking this Yes allows you to enter an email or phone number. Information for unlocking your account will then be sent to you via SMS or email, based on your choice.</div><input type='hidden' name='id' id='deleteStudentExitInterviewId' />",
    actions: [
        { text: 'Close' },
        {
            text: 'Yes',
            primary: true,
            action: function () {
                sendReactivationEmailLink();
            },
        },
    ],
    animation: defaultOpenAnimation(),
    open: onOpenDeleteDialog('#reSendActivationModal'),
    visible: false,
});
$('#tcsiTabStrip').kendoTabStrip({
    animation: defaultOpenAnimation(),
    select: onSelectTabForTcsiTabStrip,
});
$('#disabilityFormDiv').on('change', '[id^=name_]', function (e) {
    e.preventDefault();
    let id = $(this).attr('data-id');
    if ($(this).prop('checked') == true) {
        $('#end_' + id).attr('required', 'required');
        $('#start_' + id).attr('required', 'required');
    } else {
        $('#end_' + id).removeAttr('required');
        $('#start_' + id).removeAttr('required');
    }
});
$('#disabilityFormDiv').on('change', '[id^=start_]', function (e) {
    e.preventDefault();
    handleDateValidation(this, true);
});
$('#disabilityFormDiv').on('change', '[id^=end_]', function (e) {
    e.preventDefault();
    handleDateValidation(this, false);
});
/*$('#studentSanctionGrid').kendoTooltip({
    filter: "td .action-only",
    position: "bottom-left",
    // showOn: "click",
    content: function(e){
        let dataItem = $('#studentSanctionGrid').data("kendoGrid").dataItem(e.target.closest('tr'));
        let refund = (dataItem.refund > 0)? dataItem.refund:"N"
        return kendo.template($("#studentSanctionActionTemplate").html())({
            'id': dataItem.id
        });
    }
});*/

$(addTcsiCreditOfferModalId).kendoWindow(
    openCenterWindow('Add Additional Previous Study RPL or Credit Offered')
);
$(editTcsiCreditOfferModalId).kendoWindow(
    openCenterWindow('Update Additional Previous Study RPL or Credit Offered')
);
$(deleteTcsiCreditOfferModalId).kendoDialog({
    width: '400px',
    title: 'Delete Credit Offer',
    content:
        "Are you sure you want to delete this Credit Offered data ? <input type='hidden' name='id' id='deleteStudTcsiCreditOfferId' />",
    actions: [
        { text: 'Close' },
        {
            text: 'Yes',
            primary: true,
            action: function () {
                let primaryId = $(deleteTcsiCreditOfferModalId)
                    .find('#deleteStudTcsiCreditOfferId')
                    .val();
                deleteTcsiCreditOfferData(primaryId);
            },
        },
    ],
    animation: defaultOpenAnimation(),
    open: onOpenDeleteDialog(deleteTcsiCreditOfferModalId),
    visible: false,
});

function onBoundStudentSanction(e) {
    if ($('#studentSanctionGrid').data('kendoGrid').dataSource.view().length > 0) {
        $(document).find('#viewSanctionModal').find('.noDataSanction').addClass('hidden');
        $(document).find('#viewSanctionModal').find('.sanctionData').removeClass('hidden');
    } else {
        $(document).find('#viewSanctionModal').find('.noDataSanction').removeClass('hidden');
        $(document).find('#viewSanctionModal').find('.sanctionData').addClass('hidden');
    }
}
function manageIsShowMsg(isShowMsg, columnId) {
    var bgcolor = 'red';
    if (columnId == 1) {
        if (isShowMsg == '1') {
            bgcolor = 'green';
            isShowMsg = 'Yes';
        } else {
            bgcolor = 'red';
            isShowMsg = 'No';
        }
        let result =
            "<span class='inline-flex items-center justify-center px-3 py-1 bg-" +
            bgcolor +
            "-100 rounded-full'><span class='text-xs leading-5 text-center text-" +
            bgcolor +
            "-800 whitespace-nowrap'>" +
            isShowMsg +
            '</span></span>';
        return result;
    } else {
        if (isShowMsg == '1') {
            bgcolor = 'green';
            isShowMsg = 'Active';
        } else {
            bgcolor = 'red';
            isShowMsg = 'Not Active';
        }
        let result =
            "<span class='inline-flex items-center justify-center px-3 py-1 bg-" +
            bgcolor +
            "-100 rounded-full'><span class='text-xs leading-5 text-center text-" +
            bgcolor +
            "-800 whitespace-nowrap'>" +
            isShowMsg +
            '</span></span>';
        return result;
    }
}
function deleteStudentSanction(primaryID) {
    if (primaryID > 0) {
        ajaxActionV2('api/student-sanction-delete', 'POST', { id: primaryID }, function (response) {
            notificationDisplay(response.message, '', response.status);
            reloadGrid('#studentSanctionGrid');
        });
    }
}
function onBoundStudentExitInterview(e) {
    if ($('#studentExitInterviewGrid').data('kendoGrid').dataSource.view().length > 0) {
        $(document).find('#viewExitInterviewModal').find('.noDataExitInterview').addClass('hidden');
        $(document).find('#viewExitInterviewModal').find('.sanctionData').removeClass('hidden');
    } else {
        $(document)
            .find('#viewExitInterviewModal')
            .find('.noDataExitInterview')
            .removeClass('hidden');
        $(document).find('#viewExitInterviewModal').find('.sanctionData').addClass('hidden');
    }
}
function manageCourseName(course_name) {
    let courseName = course_name == null ? '-' : course_name;
    return (
        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>" +
        courseName +
        '</div>'
    );
}
function manageExitType(isExitType) {
    var bgcolor = 'green';
    var isShowMsg = 'College Exit';
    if (isExitType == 'Course Exit') {
        bgcolor = 'red';
        isShowMsg = 'Course Exit';
    } else {
        bgcolor = 'green';
        isShowMsg = 'College Exit';
    }
    let result =
        "<span class='inline-flex items-center justify-center px-3 py-1 bg-" +
        bgcolor +
        "-100 rounded-full'><span class='text-xs leading-5 text-center text-" +
        bgcolor +
        "-800 whitespace-nowrap'>" +
        isShowMsg +
        '</span></span>';
    return result;
}
function studentExitInterviewGridManageAction(primaryId) {
    return `<div class="action-div action-only flex justify-start items-center space-x-1">
                <div class="inline-flex flex-col items-center justify-center w-6 h-6 p-0.5" data-id="${primaryId}">
                    <span class="k-icon k-i-more-horizontal" style="color: #9CA3AF;"></span>
                </div>
            </div>`;
}
function deleteStudentExitInterview(primaryID) {
    if (primaryID > 0) {
        ajaxActionV2(
            'api/delete-student-exit-interview',
            'POST',
            { id: primaryID },
            function (response) {
                notificationDisplay(response.message, '', response.status);
                reloadGrid('#studentExitInterviewGrid');
            }
        );
    }
}
function sendReactivationEmailLink() {
    startAjaxLoader();
    ajaxActionV2(
        'api/send-reactivation-email-student',
        'POST',
        { student_id: selectedDataArr.student_id },
        function (response) {
            stopAjaxLoader();
            notificationDisplay(response.message, '', response.status);
            /*if(response.status == 'success'){
                $("#reSendActivationModal").data("kendoDialog").close();
            }*/
        },
        true
    );
}
function sendResetPasswordLink() {
    startAjaxLoader();
    ajaxActionV2(
        'api/send-student-password-reset-link',
        'POST',
        { student_id: selectedDataArr.student_id },
        function (response) {
            stopAjaxLoader();
            notificationDisplay(response.message, '', response.status);
            /*if(response.status == 'success'){
                $("#resetPasswordModal").data("kendoDialog").close();
            }*/
        },
        true
    );
}
function onSelectTabForTcsiTabStrip(e) {
    let tabName = $(e.item).find('> .k-link').attr('data-name');
    if (tabName == 'tcsi') {
        loadTcsiInformationForm();
    } else if (tabName == 'disability_information') {
        loadDisabilityInformationForm();
    } else if (tabName == 'sa_help') {
        loadSaHelpForm();
    } else if (tabName == 'os_help') {
        loadOsHelpForm();
    } else if (tabName == 'credit_offer') {
        studyRplOrCreditOfferData();
    } else if (tabName == 'student_course_information') {
        loadStudentCourseInformationForm();
    }
}
function loadTcsiInformationForm() {
    ajaxActionV2('api/get-course-tab-data', 'POST', selectedDataArr, function (response) {
        let responseData = response.data;
        let responseArr = {
            data: responseData.courseSummary.currentCourseSummary,
            getResultCalculationMethod: responseData.courseSummary.getResultCalculationMethod,
            studentDetails: responseData.courseSummary.studentDetails,
        };
        $(document)
            .find('.studentCourseDetailHeader')
            .html(kendo.template($('#studentCourseDetailHeaderTemplate').html())(responseArr));
    });
    ajaxActionV2('api/get-tcsi-student-data', 'POST', selectedDataArr, function (response) {
        let tcsiStudInfo = response.data.studentTcsiDetail;
        tcsiStudentCourseList = response.data.studentCourseList;
        let E561CreditBasisCode = response.data.E561CreditBasisCode;
        let E566CreditProviderCode = response.data.E566CreditProviderCode;
        renderTcsiCreditOfferForm(E561CreditBasisCode, E566CreditProviderCode);

        let addTCSIDetailsForm = $('#addTCSIDetailsForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                grid: { cols: 12, gutter: 16 },
                items: [
                    // {
                    //     field: "Old_Student_ID",
                    //     label: "Old Student ID",
                    //     colSpan: 6,
                    //     attributes: {
                    //         disabled: true,
                    //         placeholder: "Enter Old Student ID",
                    //     },
                    // },
                    {
                        field: 'citizenship_code',
                        label: 'Citizenship Code(E358)',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Citizenship Code',
                            dataSource: response.data.arrCitizenshipCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        // validation: { required: true },
                    },
                    {
                        field: 'employment_status',
                        label: 'Employment Status',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Employment Status',
                            dataSource: response.data.arrEmploymentStatus,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        // validation: { required: true },
                    },
                    {
                        field: 'citizenship_effective_from_date',
                        label: 'Citizenship Effective From Date(E609)',
                        colSpan: 6,
                        attributes: {
                            placeholder: 'Select Citizenship Effective From Date',
                        },
                        editor: 'DatePicker',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                        validation: {
                            // required: true,
                            // validateDate: function (input) {
                            //     if (
                            //         input.attr("id") ==
                            //         "citizenship_effective_from_date"
                            //     ) {
                            //         return validDate(input.val());
                            //     } else {
                            //         return true;
                            //     }
                            // },
                        },
                    },
                    {
                        field: 'tax_file_number',
                        label: 'Tax File Number(E416)',
                        colSpan: 6,
                        attributes: {
                            placeholder: 'Enter Tax File Number',
                        },
                        // validation: { required: true }
                    },
                    {
                        field: 'year_of_arrival_in_australia',
                        label: 'Year of Arrival in Australia(E347)',
                        colSpan: 6,
                        attributes: {
                            placeholder: 'Enter Year of Arrival in Australia (Format YYYY)',
                            class: 'number-only',
                            maxlength: 4,
                        },
                        validation: {
                            // required: true,
                            custom: function (input) {
                                if (input.is("[name='year_of_arrival_in_australia']")) {
                                    let value = parseInt(input.val(), 10);
                                    if (value < 1900 || value > 9999) {
                                        notificationDisplay(
                                            'Year value must be between 1900 and 9999',
                                            '',
                                            'error'
                                        );
                                        return false;
                                    }
                                }
                                return true;
                            },
                        },
                    },
                    {
                        field: 'chessn_number',
                        label: 'CHESSN Number(E488)',
                        colSpan: 12,
                        attributes: { placeholder: 'Enter CHESSN Number' },
                        // validation: { required: true },
                    },
                    {
                        field: 'education_of_parent1',
                        label: 'Education of Parent 1(E573)',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Education of Parent 1',
                            dataSource: response.data.arrEducationOfParent1,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        // validation: { required: true },
                    },
                    {
                        field: 'education_of_parent2',
                        label: 'Education of Parent 2(E574)',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Education of Parent 2',
                            dataSource: response.data.arrEducationOfParent2,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        // validation: { required: true },
                    },
                    {
                        field: 'level_left_school',
                        label: 'Level Left School(E612)',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Level Left School',
                            dataSource: response.data.arrLevelLeftSchool,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        // validation: { required: true },
                    },
                    {
                        field: 'year_of_left_school',
                        label: 'Year of left school(E572)',
                        colSpan: 6,
                        attributes: {
                            placeholder: 'Enter Year of Left School (Format YYYY)',
                            class: 'number-only',
                            maxlength: 4,
                        },
                        validation: {
                            // required: true,
                            custom: function (input) {
                                if (input.is("[name='year_of_left_school']")) {
                                    let value = parseInt(input.val(), 10);
                                    if (value < 1900 || value > 9999) {
                                        notificationDisplay(
                                            'Year value must be between 1900 and 9999',
                                            '',
                                            'error'
                                        );
                                        return false;
                                    }
                                }
                                return true;
                            },
                        },
                    },
                    {
                        field: 'attended_year_12',
                        label: 'Attended Year 12?',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Level Left School',
                            dataSource: response.data.arrAttendedYear12,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        // validation: { required: true },
                    },
                    {
                        field: 'attended_hep',
                        label: 'Attended HEP?',
                        colSpan: 12,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Attended HEP',
                            dataSource: response.data.arrAttendedHep,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        // validation: { required: true },
                    },
                ],
                buttonsTemplate: setWindowFooterTemplate('SAVE'),
                submit: function (ev) {
                    ev.preventDefault();
                    let dataArr = formValidateAndReturnFormData('#addTCSIDetailsForm');
                    dataArr['id'] = tcsiStudInfo ? tcsiStudInfo.id : 0;
                    if (dataArr) {
                        ajaxActionV2(
                            'api/save-tcsi-student-details',
                            'POST',
                            dataArr,
                            function (response) {
                                notificationDisplay(response.message, '', response.status);
                                if (response.status == 'success') {
                                    // closeKendoWindow("#viewStudentTCSIModal");
                                }
                            }
                        );
                    }
                    return false;
                },
            });
        if (tcsiStudInfo) {
            addTCSIDetailsForm.data('kendoForm').setOptions({
                formData: {
                    citizenship_code: tcsiStudInfo.citizenship_code,
                    employment_status: tcsiStudInfo.employment_status,
                    citizenship_effective_from_date: tcsiStudInfo.citizenship_effective_from_date,
                    tax_file_number: tcsiStudInfo.tax_file_number,
                    year_of_arrival_in_australia: tcsiStudInfo.year_of_arrival_in_australia,
                    chessn_number: tcsiStudInfo.chessn_number,
                    education_of_parent1: tcsiStudInfo.education_of_parent1,
                    education_of_parent2: tcsiStudInfo.education_of_parent2,
                    level_left_school: tcsiStudInfo.level_left_school,
                    year_of_left_school: tcsiStudInfo.year_of_left_school,
                    attended_year_12: tcsiStudInfo.attended_year_12,
                    attended_hep: tcsiStudInfo.attended_hep,
                },
            });
        }

        kendoWindowOpen('#viewStudentTCSIModal');
    });
}
function validDate(value) {
    var dateFormat = /^\d{2}-\d{2}-\d{4}$/;

    if (value.match(dateFormat)) {
        var parts = value.split('-');
        var day = parseInt(parts[0], 10);
        var month = parseInt(parts[1], 10);
        var year = parseInt(parts[2], 10);

        var dateObj = new Date(year, month - 1, day); // Month is 0-based

        if (
            dateObj.getFullYear() === year &&
            dateObj.getMonth() === month - 1 &&
            dateObj.getDate() === day
        ) {
            return true;
        }
    }
    return false;
}
function loadDisabilityInformationForm() {
    ajaxActionV2('api/get-tcsi-disability-info', 'POST', selectedDataArr, function (response) {
        $(document)
            .find('#disabilityFormDiv')
            .html(
                kendo.template($('#disabilityFormTemplate').html())(response.data.arrStudStatusCode)
            );
        $('.datePickerDisabilityInfo').kendoDatePicker({
            format: dateFormatFrontSideJS,
        });
        $.each(response.data.studentCourseInformationDetail, function (i, item) {
            $('#start_' + item.code).kendoDatePicker({
                value: item.start,
                format: dateFormatFrontSideJS,
            });
            $('#end_' + item.code).kendoDatePicker({
                value: item.end,
                format: dateFormatFrontSideJS,
            });
            $('#name_' + item.code).prop('checked', true);
            $('#end_' + item.code).attr('required', 'required');
            $('#start_' + item.code).attr('required', 'required');
        });
    });
}
function handleDateValidation(elementId, isStartDate) {
    let id = $(elementId).attr('data-id');
    let date = $(elementId).data('kendoDatePicker').value();
    let counterpartId = isStartDate ? '#end_' + id : '#start_' + id;
    let counterpartDate = $(counterpartId).data('kendoDatePicker').value();
    if (counterpartDate) {
        if ((isStartDate && date >= counterpartDate) || (!isStartDate && date <= counterpartDate)) {
            $(counterpartId).data('kendoDatePicker').value('');
            notificationDisplay("To date can't be less than or equal to from date", '', 'error');
        }
    }
}
function loadSaHelpForm() {
    ajaxActionV2('api/get-sa-help-data', 'POST', selectedDataArr, function (response) {
        let resSAhelpData = response.data.studentSaHelpDetail;
        let addSaHelpForm = $('#addSaHelpForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'debt_incurral_date',
                        label: 'Debit Incurral Date',
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Select Debit Incurral Date',
                        },
                        editor: 'DatePicker',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                    },
                    {
                        field: 'student_status_code',
                        label: 'Student Status Code',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Student Status Code',
                            dataSource: response.data.arrStudStatusCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        validation: { required: true },
                    },
                    {
                        field: 'amount_charged',
                        label: 'Amount Charged',
                        colSpan: 4,
                        attributes: { placeholder: 'Enter Amount Charged' },
                        editor: customNumberInput,
                        validation: { required: true },
                    },
                    {
                        field: 'upfront_fee',
                        label: 'Upfront Fee',
                        colSpan: 4,
                        attributes: { placeholder: 'Enter Upfront Fee' },
                        editor: customNumberInput,
                        validation: { required: true },
                    },
                    {
                        field: 'help_loan_amount',
                        label: 'Help Loan Amount',
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Enter Help Loan Amount',
                        },
                        editor: customNumberInput,
                        validation: { required: true },
                    },
                    {
                        field: 'reporting_year',
                        label: 'Reporting Year',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Reporting Year',
                            dataSource: response.data.arrReportingYear,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        validation: { required: true },
                    },
                    {
                        field: 'reporting_period',
                        label: 'Reporting Period',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Reporting Period',
                            dataSource: response.data.arrReportingPeriod,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        validation: { required: true },
                    },
                ],
                buttonsTemplate: setWindowFooterTemplate('SAVE'),
                submit: function (ev) {
                    ev.preventDefault();
                    let dataArr = formValidateAndReturnFormData('#addSaHelpForm');
                    dataArr['id'] = resSAhelpData ? resSAhelpData.id : 0;
                    if (dataArr) {
                        ajaxActionV2(
                            'api/save-student-sa-help-details',
                            'POST',
                            dataArr,
                            function (response) {
                                notificationDisplay(response.message, '', response.status);
                                if (response.status == 'success') {
                                    // closeKendoWindow("#viewStudentTCSIModal");
                                }
                            }
                        );
                    }
                    return false;
                },
            });
        if (resSAhelpData) {
            addSaHelpForm.data('kendoForm').setOptions({
                formData: {
                    debt_incurral_date: resSAhelpData.debt_incurral_date,
                    amount_charged: resSAhelpData.amount_charged,
                    reporting_year: resSAhelpData.reporting_year,
                    student_status_code: resSAhelpData.student_status_code,
                    upfront_fee: resSAhelpData.upfront_fee,
                    reporting_period: resSAhelpData.reporting_period,
                    help_loan_amount: resSAhelpData.help_loan_amount,
                },
            });
        }
    });
}
function loadOsHelpForm() {
    ajaxActionV2('api/get-os-help-data', 'POST', selectedDataArr, function (response) {
        let resOShelpData = response.data.studentOsHelpDetail;
        let template =
            "<div class='inline-flex space-x-1 items-end justify-between'><p class='text-sm leading-4'>#: Name #</p><div class='flex items-center justify-center px-2.5 py-0.5 bg-#:bg_color#-100 rounded-md'><p class='text-xs leading-4 text-center text-gray-500 truncate'>#: status #</p></div></div>";

        let addCourseOsHelpForm = $('#addCourseOsHelpForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'course_id',
                        label: '',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Course',
                            optionLabelTemplate: '<span> Select Course</span>',
                            valueTemplate: template,
                            template: template,
                            dataSource: response.data.arrCourseList,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                            select: function (e) {
                                setCourseInformationForOsHelpData(e.dataItem.Id);
                            },
                        },
                        validation: { required: true },
                    },
                ],
                buttonsTemplate: '',
            });
        let addOsHelpForm = $('#addOsHelpForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'debt_incurral_date',
                        label: 'Debt Incurral Date',
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Enter Debt Incurral Date',
                        },
                        editor: 'DatePicker',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                    },
                    {
                        field: 'student_status_code',
                        label: 'Student Status Code',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Student Status Code',
                            dataSource: response.data.arrStudStatusCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        validation: { required: true },
                    },
                    {
                        field: 'loan_fee',
                        label: 'Loan Fee',
                        colSpan: 4,
                        attributes: { placeholder: 'Enter Loan Fee' },
                        editor: customNumberInput,
                        validation: { required: true },
                    },
                    {
                        field: 'payment_amount',
                        label: 'Payment Amount',
                        attributes: { placeholder: 'Enter Payment Amount' },
                        colSpan: 4,
                        editor: customNumberInput,
                        validation: { required: true },
                    },
                    {
                        field: 'study_period_commencement_date',
                        label: 'Study Period Commencement Date',
                        attributes: {
                            placeholder: 'Select Study Period Commencement Date',
                        },
                        colSpan: 4,
                        editor: 'DatePicker',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                        validation: { required: true },
                    },

                    {
                        field: 'primary_study_country',
                        label: 'Primary Study Country',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Primary Study Country',
                            dataSource: response.data.arrStudCountry,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        validation: { required: true },
                    },

                    {
                        field: 'secondary_study_country',
                        label: 'Secondary Study Country',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Secondary Study Country',
                            dataSource: response.data.arrStudCountry,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        validation: { required: true },
                    },

                    {
                        field: 'language',
                        label: 'Language',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Language',
                            dataSource: response.data.arrLanguageList,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        validation: { required: true },
                    },
                    {
                        field: 'language_study_commencement_date',
                        label: 'Language Study Commencement Date',
                        attributes: {
                            placeholder: 'Select Language Study Commencement Date',
                        },
                        colSpan: 4,
                        editor: 'DatePicker',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                    },
                    {
                        field: 'reporting_year',
                        label: 'Reporting Year',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Reporting Year',
                            dataSource: response.data.arrReportingYear,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                    },
                    {
                        field: 'reporting_period',
                        label: 'Reporting Period',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Reporting Period',
                            dataSource: response.data.arrReportingPeriod,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                    },
                ],
                buttonsTemplate: setWindowFooterTemplate('SAVE'),
                submit: function (ev) {
                    ev.preventDefault();
                    let dataArr = formValidateAndReturnFormData('#os_help_div');
                    dataArr['id'] = resOShelpData ? resOShelpData.id : 0;
                    if (dataArr) {
                        ajaxActionV2(
                            'api/save-student-os-help-details',
                            'POST',
                            dataArr,
                            function (response) {
                                notificationDisplay(response.message, '', response.status);
                                if (response.status == 'success') {
                                    // closeKendoWindow("#viewStudentTCSIModal");
                                }
                            }
                        );
                    }
                    return false;
                },
            });
        if (resOShelpData) {
            addCourseOsHelpForm.data('kendoForm').setOptions({
                formData: {
                    course_id: resOShelpData.course_id,
                },
            });
            addOsHelpForm.data('kendoForm').setOptions({
                formData: {
                    course_id: resOShelpData.course_id,
                    debt_incurral_date: resOShelpData.debt_incurral_date,
                    student_status_code: resOShelpData.student_status_code,
                    loan_fee: resOShelpData.loan_fee,
                    payment_amount: resOShelpData.payment_amount,
                    study_period_commencement_date: resOShelpData.study_period_commencement_date,
                    primary_study_country: resOShelpData.primary_study_country,
                    secondary_study_country: resOShelpData.secondary_study_country,
                    language: resOShelpData.language,
                    language_study_commencement_date:
                        resOShelpData.language_study_commencement_date,
                    reporting_year: resOShelpData.reporting_year,
                    reporting_period: resOShelpData.reporting_period,
                },
            });
        }
    });
}
function studyRplOrCreditOfferData() {
    let gridPostDataArr = {
        college_id: collegeId,
        student_id: studentId,
    };

    if (tcsiCreditOfferFlag) {
        tcsiCreditOfferFlag = false;

        $(tcsiCreditOfferGridId).kendoGrid({
            dataSource: customDataSource(
                'api/get-student-tcsi-credit-offers',
                {
                    course_code: { type: 'string' },
                    credit_used_value: { type: 'string' },
                    credit_basis_code: { type: 'string' },
                    credit_provider_code: { type: 'string' },
                },
                gridPostDataArr
            ),
            pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            columns: [
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#: course_code #</div>",
                    field: 'course_code',
                    title: 'Course Id',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: credit_used_value #</div>",
                    field: 'credit_used_value',
                    title: 'Credit Use Value(E560)',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: credit_basis_code #</div>",
                    field: 'credit_basis_code',
                    title: 'Credit Basis Code(E561)',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: credit_provider_code #</div>",
                    field: 'credit_provider_code',
                    title: 'Credit Provider Code(E566)',
                },
                {
                    width: 80,
                    headerTemplate:
                        "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>ACTION</a>",
                    field: 'action',
                    title: 'ACTION',
                    filterable: false,
                    sortable: false,
                    template: function (dataItem) {
                        return `<div class="action-div tw-action tw-action--autohide flex justify-start items-center space-x-1">
                                    <div class="action-only tw-btn-action inline-flex flex-col items-center justify-center w-6 h-6 p-0.5" data-id="${dataItem.id}">
                                        <span class="k-icon k-i-more-horizontal" style="color: #9CA3AF;"></span>
                                    </div>
                                </div>`;
                    },
                },
            ],
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                togglePagination(tcsiCreditOfferGridId);
            },
        });

        //customGridHtml(tcsiCreditOfferGridId);

        $(tcsiCreditOfferGridId).kendoTooltip({
            filter: 'td .action-only',
            position: 'bottom-left',
            showOn: 'click',
            content: function (e) {
                let dataItem = $(tcsiCreditOfferGridId)
                    .data('kendoGrid')
                    .dataItem(e.target.closest('tr'));
                return kendo.template($('#studTcsiCreditOfferActionTemplate').html())({
                    id: dataItem.id,
                });
            },
            show: function (e) {
                manageTooltipPosition(e);
            },
        });
    } else {
        refreshGrid(tcsiCreditOfferGridId, gridPostDataArr);
    }
}
function loadStudentCourseInformationForm() {
    ajaxActionV2('api/get-tcsi-student-course-info', 'POST', selectedDataArr, function (response) {
        let responseData = response.data;
        let template =
            "<div class='inline-flex space-x-1 items-end justify-between'><p class='text-sm leading-4'>#: Name #</p><div class='flex items-center justify-center px-2.5 py-0.5 bg-#:bg_color#-100 rounded-md'><p class='text-sm leading-4 text-center text-gray-500  truncate'>#: status #</p></div></div>";

        let addCourseList = $('#addCourseList')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'course_id',
                        label: '',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Course',
                            optionLabelTemplate: '<span> Select Course</span>',
                            dataSource: responseData.arrCourseList,
                            valueTemplate: template,
                            template: template,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                            select: function (e) {
                                setCourseInformationData(e.dataItem.Id);
                            },
                        },
                        validation: { required: true },
                    },
                ],
                buttonsTemplate: setWindowFooterTemplate('SAVE'),
                submit: function (ev) {
                    ev.preventDefault();
                    let dataArr = formValidateAndReturnFormData('#student_course_information');
                    dataArr['id'] = responseData.studentCourseInformationDetail
                        ? responseData.studentCourseInformationDetail.id
                        : 0;
                    if (dataArr) {
                        ajaxActionV2(
                            'api/save-tcsi-student-course-info',
                            'POST',
                            dataArr,
                            function (response) {
                                notificationDisplay(response.message, '', response.status);
                                if (response.status == 'success') {
                                    // closeKendoWindow("#viewStudentTCSIModal");
                                }
                            }
                        );
                    }
                    return false;
                },
            });
        let addStudentCourseInformationForm = $('#addStudentCourseInformationForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'basis_admission_code',
                        label: 'Basis For Admission Code(E327)',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Basis For Admission Code',
                            dataSource: responseData.arrBasisForAdmissionCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'mode_attendance_code',
                        label: 'Mode Of Attendance Code',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Mode Of Attendance Code',
                            dataSource: responseData.arrModeOfAttendanceCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'attendance_type',
                        label: 'Attendance Type(E330)',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Attendance Type',
                            dataSource: responseData.arrTypeofAttendanceCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'remission_reason_code',
                        label: 'Remission Reason Code(E446)',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Remission Reason Code',
                            dataSource: responseData.arrRemissionReasonCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'course_outcome_code', //TODO::change
                        label: 'Course Outcome Code(E599)',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Course Outcome Code',
                            dataSource: responseData.arrCourseOutcomeCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'highest_attainment_code',
                        label: 'Highest Attainment Code(E620)',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Highest Attainment Code',
                            dataSource: responseData.arrHighestAttainmentCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'atar',
                        label: 'ATAR(E632)',
                        colSpan: 4,
                        attributes: { placeholder: 'Enter ATAR' },
                        //validation: { required: true },
                    },
                    {
                        field: 'previous_rts_eftsl',
                        label: 'Previous RTS EFTSL',
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Enter Previous RTS EFTSL',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'total_exemption_granted',
                        label: 'Total Exemption Granted',
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Enter Total Exemption Granted',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'completion_percentage_code',
                        label: 'Completion Percentage Code',
                        attributes: {
                            placeholder: 'Enter Completion Percentage Code',
                        },
                        colSpan: 4,
                        //validation: { required: true },
                    },
                    {
                        field: 'selection_rank',
                        label: 'Selection Rank(E605)',
                        colSpan: 4,
                        attributes: { placeholder: 'Enter Selection Rank' },
                        //validation: { required: true },
                    },
                ],
                buttonsTemplate: '',
            });
        let addStudentSpecialisationCodeForm = $('#addStudentSpecialisationCodeForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'broad_type',
                        label: 'Broad Type',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Broad Type',
                            dataSource: responseData.arrBroadTypeArr,
                            select: function (e) {
                                if (e.dataItem) {
                                    let postArr = {
                                        broad_id: e.dataItem.Id,
                                        college_id: selectedDataArr.college_id,
                                    };
                                    $('#narrow_type').kendoDropDownList({
                                        dataTextField: 'Name',
                                        dataValueField: 'Id',
                                        dataSource: getDropdownDataSource(
                                            'get-narrow-type-list-data',
                                            postArr
                                        ),
                                        dataBound: function (e) {
                                            this.select(0);
                                            this.trigger('change');
                                            this.trigger('select');
                                        },
                                        select: function (e) {
                                            let postArr = {
                                                narrow_id: $('#narrow_type')
                                                    .data('kendoDropDownList')
                                                    .value(),
                                                college_id: selectedDataArr.college_id,
                                            };
                                            $('#narrow_sub_type').kendoDropDownList({
                                                dataTextField: 'Name',
                                                dataValueField: 'Id',
                                                dataSource: getDropdownDataSource(
                                                    'get-sub-narrow-type-list-data',
                                                    postArr
                                                ),
                                                dataBound: function (e) {
                                                    this.select(0);
                                                    this.trigger('change');
                                                    this.trigger('select');
                                                },
                                            });
                                        },
                                    });
                                }
                            },
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'narrow_type',
                        label: 'Narrow Type',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Narrow Type',
                            dataSource: responseData.arrNarrowTypeArr,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                            select: function (e) {
                                if (e.dataItem) {
                                    let postArr = {
                                        narrow_id: e.dataItem.Id,
                                        college_id: selectedDataArr.college_id,
                                    };
                                    $('#narrow_sub_type').kendoDropDownList({
                                        dataTextField: 'Name',
                                        dataValueField: 'Id',
                                        dataSource: getDropdownDataSource(
                                            'get-sub-narrow-type-list-data',
                                            postArr
                                        ),
                                        dataBound: function (e) {
                                            this.select(0);
                                            this.trigger('change');
                                            // this.trigger("select");
                                        },
                                    });
                                }
                            },
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'narrow_sub_type',
                        label: 'Narrow Sub Type(E463)',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Narrow Sub Type',
                            dataSource: responseData.arrSubNarrowTypeArr,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                ],
                buttonsTemplate: '',
            });
        let addHigherDegreeResearchInformationForm = $('#addHigherDegreeResearchInformationForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'student_separation_code',
                        label: 'Student Separation Code',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Student Separation Code',
                            dataSource: responseData.arrStudStatusCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'end_user_engagement_code',
                        label: 'End User Engagement Code(E593)',
                        colSpan: 4,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select End User Engagement Code',
                            dataSource: responseData.arrStudStatusCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'thesis_end_user_engagement_effective_from',
                        label: 'Thesis End User Engagement Effective From Date(E609)',
                        editor: 'DatePicker',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Select Thesis End User Engagement Effective From Date',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'thesis_end_user_engagement_effective_to',
                        label: 'Thesis End User Engagement Effective To Date(E610)',
                        editor: 'DatePicker',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Select Thesis End User Engagement Effective To Date',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'thesis_submission_date',
                        label: 'Thesis Submission Date(E591)',
                        editor: 'DatePicker',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Select Thesis Submission Date',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'completion_date',
                        label: 'Course Outcome Date(E592)',
                        colSpan: 4,
                        editor: 'DatePicker',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                        attributes: {
                            placeholder: 'Select Completion Date',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'primary_field_research_code',
                        label: 'Primary Field Of Research Code(E594)',
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Enter Primary Field Of Research Code',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'secondary_field_research_code',
                        label: 'Secondary Field Of Research Code(E595)',
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Enter Secondary Field Of Research Code',
                        },
                        //validation: { required: true },
                    },
                ],
                buttonsTemplate: '',
            });
        let addAdditionalInformationForm = $('#addAdditionalInformationForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'commencement_date',
                        label: 'Commencement Date(E534)',
                        colSpan: 4,
                        editor: 'DatePicker',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                        attributes: {
                            placeholder: 'Select Commencement Date',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'academic_organisational_unit_code',
                        label: 'Academic Organisational Unit(AOU) Code',
                        colSpan: 4,
                        attributes: {
                            placeholder: 'Enter Academic Organisational Unit(AOU) Code',
                        },
                        //validation: { required: true },
                    },
                ],
                buttonsTemplate: '',
            });
        let addPreviousStudyRPLForm = $('#addPreviousStudyRPLForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                formData: {
                    is_credit_provider: false,
                },
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'is_credit_provider',
                        label: 'Previous Study RPL OR Credit Offered',
                        colSpan: 12,
                        attributes: {
                            placeholder: 'Enter Previous Study RPL OR Credit Offered',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'credit_provider_code',
                        label: 'Previous Study RPL or Credit Offered Code',
                        colSpan: 6,
                        attributes: {
                            placeholder: 'Enter Previous Study RPL or Credit Offered Code',
                        },
                        //validation: { required: true },
                    },
                ],
                buttonsTemplate: '',
                /*buttonsTemplate: setWindowFooterTemplate("SAVE"),
                submit: function(ev) {
                    ev.preventDefault();
                    let dataArr = formValidateAndReturnFormData("#student_course_information");
                    dataArr['id'] = (responseData.studentCourseInformationDetail)? responseData.studentCourseInformationDetail.id:0;
                    if(dataArr) {
                        ajaxActionV2("api/save-tcsi-student-course-info", "POST", dataArr, function (response) {
                            notificationDisplay(response.message, '', response.status);
                            if(response.status == 'success'){
                                closeKendoWindow("#viewStudentTCSIModal");
                            }
                        });
                    }
                    return false;
                }*/
            });
        let tcsiStudCourseData = responseData.studentCourseInformationDetail;
        if (tcsiStudCourseData) {
            addCourseList.data('kendoForm').setOptions({
                formData: {
                    course_id: tcsiStudCourseData.course_id,
                },
            });
            addStudentCourseInformationForm.data('kendoForm').setOptions({
                formData: {
                    basis_admission_code: tcsiStudCourseData.basis_admission_code,
                    mode_attendance_code: tcsiStudCourseData.mode_attendance_code,
                    attendance_type: tcsiStudCourseData.attendance_type,
                    remission_reason_code: tcsiStudCourseData.remission_reason_code,
                    course_outcome_code: tcsiStudCourseData.course_outcome_code,
                    highest_attainment_code: tcsiStudCourseData.highest_attainment_code,
                    atar: tcsiStudCourseData.atar,
                    previous_rts_eftsl: tcsiStudCourseData.previous_rts_eftsl,
                    total_exemption_granted: tcsiStudCourseData.total_exemption_granted,
                    completion_percentage_code: tcsiStudCourseData.completion_percentage_code,
                    selection_rank: tcsiStudCourseData.selection_rank,
                },
            });
            addStudentSpecialisationCodeForm.data('kendoForm').setOptions({
                formData: {
                    broad_type: tcsiStudCourseData.broad_type,
                    narrow_type: tcsiStudCourseData.narrow_type,
                    narrow_sub_type: tcsiStudCourseData.narrow_sub_type,
                },
            });
            addHigherDegreeResearchInformationForm.data('kendoForm').setOptions({
                formData: {
                    student_separation_code: tcsiStudCourseData.student_separation_code,
                    end_user_engagement_code: tcsiStudCourseData.end_user_engagement_code,
                    thesis_end_user_engagement_effective_from:
                        tcsiStudCourseData.thesis_end_user_engagement_effective_from,
                    thesis_end_user_engagement_effective_to:
                        tcsiStudCourseData.thesis_end_user_engagement_effective_to,
                    thesis_submission_date: tcsiStudCourseData.thesis_submission_date,
                    completion_date: tcsiStudCourseData.completion_date,
                    primary_field_research_code: tcsiStudCourseData.primary_field_research_code,
                    secondary_field_research_code: tcsiStudCourseData.secondary_field_research_code,
                },
            });
            addAdditionalInformationForm.data('kendoForm').setOptions({
                formData: {
                    commencement_date: tcsiStudCourseData.commencement_date,
                    academic_organisational_unit_code:
                        tcsiStudCourseData.academic_organisational_unit_code,
                },
            });
            addPreviousStudyRPLForm.data('kendoForm').setOptions({
                formData: {
                    is_credit_provider: tcsiStudCourseData.is_credit_provider ? true : false,
                    credit_provider_code: tcsiStudCourseData.credit_provider_code,
                },
            });
        }
    });
}
function setCourseInformationData(course_id) {
    var postArray = {
        college_id: collegeId,
        student_id: studentId,
        course_id: course_id,
    };
    ajaxActionV2(
        'api/get-student-course-information-from-course-id',
        'POST',
        postArray,
        function (response) {
            let tcsiStudCourseData = response.data.studentCourseInformationDetail;
            if (tcsiStudCourseData) {
                $('#addStudentCourseInformationForm')
                    .data('kendoForm')
                    .setOptions({
                        formData: {
                            basis_admission_code: tcsiStudCourseData.basis_admission_code,
                            mode_attendance_code: tcsiStudCourseData.mode_attendance_code,
                            attendance_type: tcsiStudCourseData.attendance_type,
                            remission_reason_code: tcsiStudCourseData.remission_reason_code,
                            course_outcome_code: tcsiStudCourseData.course_outcome_code,
                            highest_attainment_code: tcsiStudCourseData.highest_attainment_code,
                            atar: tcsiStudCourseData.atar,
                            previous_rts_eftsl: tcsiStudCourseData.previous_rts_eftsl,
                            total_exemption_granted: tcsiStudCourseData.total_exemption_granted,
                            completion_percentage_code:
                                tcsiStudCourseData.completion_percentage_code,
                            selection_rank: tcsiStudCourseData.selection_rank,
                        },
                    });
                $('#addStudentSpecialisationCodeForm')
                    .data('kendoForm')
                    .setOptions({
                        formData: {
                            broad_type: tcsiStudCourseData.broad_type,
                            narrow_type: tcsiStudCourseData.narrow_type,
                            narrow_sub_type: tcsiStudCourseData.narrow_sub_type,
                        },
                    });
                $('#addHigherDegreeResearchInformationForm')
                    .data('kendoForm')
                    .setOptions({
                        formData: {
                            student_separation_code: tcsiStudCourseData.student_separation_code,
                            end_user_engagement_code: tcsiStudCourseData.end_user_engagement_code,
                            thesis_end_user_engagement_effective_from:
                                tcsiStudCourseData.thesis_end_user_engagement_effective_from,
                            thesis_end_user_engagement_effective_to:
                                tcsiStudCourseData.thesis_end_user_engagement_effective_to,
                            thesis_submission_date: tcsiStudCourseData.thesis_submission_date,
                            completion_date: tcsiStudCourseData.completion_date,
                            primary_field_research_code:
                                tcsiStudCourseData.primary_field_research_code,
                            secondary_field_research_code:
                                tcsiStudCourseData.secondary_field_research_code,
                        },
                    });
                $('#addAdditionalInformationForm')
                    .data('kendoForm')
                    .setOptions({
                        formData: {
                            commencement_date: tcsiStudCourseData.commencement_date,
                            academic_organisational_unit_code:
                                tcsiStudCourseData.academic_organisational_unit_code,
                        },
                    });
                $('#addPreviousStudyRPLForm')
                    .data('kendoForm')
                    .setOptions({
                        formData: {
                            is_credit_provider: tcsiStudCourseData.is_credit_provider
                                ? true
                                : false,
                            credit_provider_code: tcsiStudCourseData.credit_provider_code,
                        },
                    });
            } else {
                $('#addStudentCourseInformationForm').data('kendoForm').clear();
                $('#addStudentSpecialisationCodeForm').data('kendoForm').clear();
                $('#addHigherDegreeResearchInformationForm').data('kendoForm').clear();
                $('#addAdditionalInformationForm').data('kendoForm').clear();
                $('#addPreviousStudyRPLForm').data('kendoForm').clear();
            }
        }
    );
}
function setCourseInformationForOsHelpData(course_id) {
    var postArray = {
        college_id: collegeId,
        student_id: studentId,
        course_id: course_id,
    };
    ajaxActionV2(
        'api/get-student-course-information-for-os-help',
        'POST',
        postArray,
        function (response) {
            let resOShelpData = response.data.studentOsHelpDetail;
            if (resOShelpData) {
                $('#addOsHelpForm')
                    .data('kendoForm')
                    .setOptions({
                        formData: {
                            debt_incurral_date: resOShelpData.debt_incurral_date,
                            student_status_code: resOShelpData.student_status_code,
                            loan_fee: resOShelpData.loan_fee,
                            payment_amount: resOShelpData.payment_amount,
                            study_period_commencement_date:
                                resOShelpData.study_period_commencement_date,
                            primary_study_country: resOShelpData.primary_study_country,
                            secondary_study_country: resOShelpData.secondary_study_country,
                            language: resOShelpData.language,
                            language_study_commencement_date:
                                resOShelpData.language_study_commencement_date,
                            reporting_year: resOShelpData.reporting_year,
                            reporting_period: resOShelpData.reporting_period,
                        },
                    });
            } else {
                $('#addOsHelpForm').data('kendoForm').clear();
            }
        }
    );
}

// Helper function to manage loading state (similar to GlobalContextLoader)
function validateFromDate(fromDate, formId) {
    var toDate = $(formId).find('#to_date').data('kendoDatePicker').value();
    return toDate === '' || new Date(fromDate) <= new Date(toDate);
}
function validateToDate(toDate, formId) {
    var fromDate = $(formId).find('#from_date').data('kendoDatePicker').value();
    return fromDate === '' || new Date(toDate) >= new Date(fromDate);
}
function renderTcsiCreditOfferForm(E561CreditBasisCode, E566CreditProviderCode) {
    if (singleTimeLoadFlag) {
        singleTimeLoadFlag = false;
        var statusTemplate =
            "<div class='inline-flex space-x-1 items-end justify-between'><p class='text-sm leading-4'>#: Name #</p><div class='flex items-center justify-center px-2.5 py-0.5 bg-#:bg_color#-100 rounded-md'><p class='text-xs leading-4 text-center text-gray-500 truncate'>#: status #</p></div></div>";

        $('#addTcsiCreditOfferForm')
            .html('')
            .kendoForm({
                orientation: 'vertical',
                type: 'group',
                validatable: defaultErrorTemplate(),
                layout: 'grid',
                grid: { cols: 6, gutter: 10 },
                items: [
                    {
                        field: 'student_course_id',
                        label: 'Course',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Course',
                            optionLabelTemplate: '<span> Select Course</span>',
                            valueTemplate: statusTemplate,
                            template: statusTemplate,
                            dataSource: tcsiStudentCourseList,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        validation: { required: true },
                    },
                    {
                        field: 'credit_used_value',
                        label: 'Credit Used Value(E560)',
                        colSpan: 6,
                        className: 'threeDigitInput',
                        editor: customFloatNumberInput,
                        validation: { required: true },
                        attributes: { placeholder: 'Enter Credit Used Value' },
                    },
                    {
                        field: 'credit_basis_code',
                        label: 'Credit Basis Code(E561)',
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Credit Basis Code',
                            dataSource: E561CreditBasisCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        colSpan: 6,
                        validation: { required: true },
                    },
                    {
                        field: 'credit_provider_code',
                        label: 'Credit Provider Code(E566)',
                        colSpan: 6,
                        validation: { required: true },
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Credit Basis Code',
                            dataSource: E566CreditProviderCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                    },
                ],
                buttonsTemplate: setModalFooterTemplate('Save'),
                submit: function (ev) {
                    ev.preventDefault();
                    saveTcsiCreditOfferData('#addTcsiCreditOfferForm');
                },
            });

        $('#editTcsiCreditOfferForm')
            .html('')
            .kendoForm({
                orientation: 'vertical',
                type: 'group',
                layout: 'grid',
                grid: { cols: 6, gutter: 10 },
                items: [
                    {
                        id: 'edit_student_course_id',
                        field: 'student_course_id',
                        label: 'Course',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Course',
                            optionLabelTemplate: '<span> Select Course</span>',
                            valueTemplate: statusTemplate,
                            template: statusTemplate,
                            dataSource: tcsiStudentCourseList,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        validation: { required: true },
                    },
                    {
                        id: 'edit_credit_used_value',
                        field: 'credit_used_value',
                        label: 'Credit Used Value(E560)',

                        colSpan: 6,
                        className: 'threeDigitInput',
                        editor: customFloatNumberInput,
                        validation: { required: true },
                        attributes: { placeholder: 'Enter Credit Used Value', maxLength: 4 },
                    },
                    {
                        id: 'edit_credit_basis_code',
                        field: 'credit_basis_code',
                        label: 'Credit Basis Code(E561)',
                        colSpan: 6,
                        validation: { required: true },
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Credit Basis Code',
                            dataSource: E561CreditBasisCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                    },
                    {
                        id: 'edit_credit_provider_code',
                        field: 'credit_provider_code',
                        label: 'Credit Provider Code(E566)',
                        colSpan: 6,
                        validation: { required: true },
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Credit Basis Code',
                            dataSource: E566CreditProviderCode,
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                    },
                ],
                //buttonsTemplate: setModalFooterTemplate("Update"),
                buttonsTemplate: `<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end px-1 py-1">
                                <button type="button" class="btn-secondary cancelBtn">
                                    <p class="text-sm font-medium leading-4 text-gray-700">Cancel</p>
                                </button>
                                <input type="hidden" id="creditOfferId" name="id" value="" />
                                <button type="submit" class="btn-primary">
                                    <p class="text-sm font-medium leading-4 text-white">Update</p>
                                </button>
                           </div>`,
                submit: function (ev) {
                    updateTcsiCreditOfferData('#editTcsiCreditOfferForm');
                    ev.preventDefault();
                    return false;
                },
            });
    }
}
$(document).on('input', '#credit_used_value', function () {
    let value = $(this).val();

    // Allow only numbers and a single dot
    value = value.replace(/[^0-9.]/g, '');

    // Ensure only one decimal point is present
    let parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join(''); // Remove extra dots
    }

    // Restrict to max 4 digits, allowing up to 3 decimal places
    if (parts.length === 2) {
        parts[1] = parts[1].substring(0, 3); // Max 3 decimal places
        value = parts[0] + '.' + parts[1];
    }

    if (value.length > 4 && parts.length === 1) {
        value = value.substring(0, 4); // Limit to 4 digits before the decimal
    }

    $(this).val(value);
});
$(document).on('input', '#edit_credit_used_value', function () {
    let value = $(this).val();

    // Allow only numbers and a single dot
    value = value.replace(/[^0-9.]/g, '');

    // Ensure only one decimal point is present
    let parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join(''); // Remove extra dots
    }

    // Restrict to max 4 digits, allowing up to 3 decimal places
    if (parts.length === 2) {
        parts[1] = parts[1].substring(0, 3); // Max 3 decimal places
        value = parts[0] + '.' + parts[1];
    }

    if (value.length > 4 && parts.length === 1) {
        value = value.substring(0, 4); // Limit to 4 digits before the decimal
    }

    $(this).val(value);
});

function saveTcsiCreditOfferData(formId) {
    if (formId !== '') {
        let validator = defaultFormValidator(formId);
        if (!validator.validate()) {
            notificationDisplay('Please fill in all the required fields.', '', 'error');
            return false;
        }

        let tempDataArr = {
            college_id: collegeId,
            student_id: studentId,
        };

        let dataArr = getSerializeFormArray(formId, tempDataArr);

        ajaxActionV2('api/save-tcsi-credit-offer-data', 'POST', dataArr, function (response) {
            if (response.status == 'success') {
                window.parent.$(addTcsiCreditOfferModalId).data('kendoWindow').close();
                reloadGrid(tcsiCreditOfferGridId);
            }
            notificationDisplay(response.message, '', response.status);
        });
    }
}
function updateTcsiCreditOfferData(formId) {
    if (formId !== '') {
        let validator = defaultFormValidator(formId);
        if (!validator.validate()) {
            notificationDisplay('Please fill in all the required fields.', '', 'error');
            return false;
        }

        //let tempDataArr = { id: primaryId };
        let dataArr = getSerializeFormArray(formId, {});

        ajaxActionV2('api/update-tcsi-credit-offer-data', 'POST', dataArr, function (response) {
            if (response.status == 'success') {
                closeKendoWindow(editTcsiCreditOfferModalId);
                reloadGrid(tcsiCreditOfferGridId);
            }
            notificationDisplay(response.message, '', response.status);
        });
    }
}
function deleteTcsiCreditOfferData(primaryID) {
    if (primaryID > 0) {
        ajaxActionV2(
            'api/delete-tcsi-credit-offer-data',
            'POST',
            { id: primaryID },
            function (response) {
                notificationDisplay(response.message, '', response.status);
                if (response.status == 'success') {
                    reloadGrid(tcsiCreditOfferGridId);
                }
            }
        );
    }
}

$('body').on('click', '.viewSanctionBtn', function (e) {
    e.preventDefault();
    ajaxActionV2('api/get-course-tab-data', 'POST', selectedDataArr, function (response) {
        let responseData = response.data;
        let responseArr = {
            data: responseData.courseSummary.currentCourseSummary,
            getResultCalculationMethod: responseData.courseSummary.getResultCalculationMethod,
            studentDetails: responseData.courseSummary.studentDetails,
        };
        var profile_pic = '';
        if (responseData.courseSummary.studentProfile == '') {
            let name = responseData.courseSummary.studentDetails[0].full_name
                .toUpperCase()
                .split(/\s+/);
            let shortName =
                name.length >= 2 ? name[0].charAt(0) + name[1].charAt(0) : name[0].substring(0, 2);
            profile_pic =
                '<div class="rounded-full"><div class="flex user-profile-pic w-16 h-16 rounded-full bg-blue-500 items-center"><span class="text-2xl flex justify-center items-center leading-6 px-1 w-full">' +
                shortName +
                '</span></div></div>';
        } else {
            profile_pic =
                '<div class="w-16 h-16 rounded-full"><img class="w-16 h-16 rounded-full" src="' +
                responseData.courseSummary.studentProfile +
                '"/></div>';
        }
        $('.studentProfilePic').html(profile_pic);
        $('.studentProfilePicName').html(responseData.courseSummary.studentDetails[0].full_name);
        $('.studentStudId').html(responseData.courseSummary.studentDetails[0].generated_stud_id);
        $('#viewSanctionModal_wnd_title').text(
            'Sanction for ' + responseData.courseSummary.studentDetails[0].full_name
        );
        $(document)
            .find('.studentCourseDetailHeader')
            .html(kendo.template($('#studentCourseDetailHeaderTemplate').html())(responseArr));
    });

    initializeActionMenu('studentSanctionGrid', 'studentSanctionActionTemplate');
    $('#studentSanctionGrid').kendoGrid({
        dataSource: customDataSource(
            'api/get-student-sanction-list',
            {
                type: { type: 'string' },
                comment: { type: 'string' },
                active_date: { type: 'string' },
                is_show_msg: { type: 'string' },
                is_active: { type: 'string' },
            },
            selectedDataArr
        ),
        pageable: customPageableArr(),
        sortable: true,
        resizable: true,
        columns: [
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: type #</div>",
                field: 'type',
                title: 'Type',
                sortable: true,
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: comment #</div>",
                field: 'comment',
                title: 'Comment',
                sortable: true,
                width: '450px',
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: active_date #</div>",
                field: 'active_date',
                title: 'Entered Time',
                sortable: true,
            },
            {
                template: function (dataItem) {
                    return manageIsShowMsg(dataItem.is_show_msg, 1);
                },
                field: 'is_show_msg',
                title: 'Show message',
                sortable: true,
            },
            {
                template: function (dataItem) {
                    return manageIsShowMsg(dataItem.is_active, 2);
                },
                field: 'is_active',
                title: 'Action Status',
                sortable: true,
            },
            {
                template: function (dataItem) {
                    return manageActionColumn(dataItem.id);
                },
                field: 'action',
                title: 'ACTION',
            },
        ],
        noRecords: noRecordTemplate(),
        dataBound: function (e) {
            onBoundStudentSanction(e);
            togglePagination('#studentSanctionGrid');
        },
    });
    kendoWindowOpen('#viewSanctionModal');
});
$('body').on('click', '.addSanctionBtn', function (e) {
    e.preventDefault();
    let addSanctionDetailsForm = $('#addSanctionDetailsForm')
        .html('')
        .kendoForm({
            orientation: 'vertical',
            layout: 'grid',
            type: 'group',
            grid: { cols: 6, gutter: 16 },
            items: [
                {
                    field: 'type',
                    editor: 'RadioGroup',
                    label: 'Type',
                    colSpan: 6,
                    editorOptions: {
                        items: [
                            { value: '1', label: 'Academic' },
                            { value: '2', label: 'Payment' },
                        ],
                        layout: 'horizontal',
                    },
                    validation: { required: true },
                },
                {
                    field: 'comment',
                    label: 'Comment',
                    editor: 'TextArea',
                    colSpan: 6,
                    editorOptions: {
                        placeholder: 'comment',
                        rows: 4,
                    },
                    validation: { required: { message: 'Enter Comment' } },
                },
                {
                    field: 'is_show_msg',
                    editor: 'Switch',
                    label: 'Show message to Student',
                    colSpan: 6,
                    attributes: {
                        class: 'tw-switch-group',
                    },
                    validation: { required: false },
                },
                {
                    field: 'is_active',
                    editor: 'Switch',
                    label: 'Set Sanction Status Active',
                    colSpan: 6,
                    attributes: {
                        class: 'tw-switch-group',
                    },
                    validation: { required: false },
                },
                {
                    field: 'active_date',
                    editor: 'DatePicker',
                    label: 'Active From',
                    editorOptions: {
                        format: dateFormatFrontSideJS,
                    },
                    validation: {
                        required: true,
                        validateDate: function (input) {
                            if (input.attr('id') == 'active_date') {
                                return validDate(input.val());
                            } else {
                                return true;
                            }
                        },
                    },
                    colSpan: 6,
                },
            ],
            buttonsTemplate: setWindowFooterTemplate('SAVE', 'submit', 'add-sanction-submit', true),
            submit: function (ev) {
                ev.preventDefault();
                let dataArr = formValidateAndReturnFormData('#addSanctionDetailsForm');
                if (dataArr) {
                    ajaxActionV2('api/save-student-sanction', 'POST', dataArr, function (response) {
                        notificationDisplay(response.message, '', response.status);
                        if (response.status == 'success') {
                            closeKendoWindow('#addSanctionModal');
                            reloadGrid('#studentSanctionGrid');
                        }
                    });
                }
                return false;
            },
        });
    addSanctionDetailsForm.data('kendoForm').setOptions({
        formData: {
            type: 1,
        },
    });
    $(document).find('.k-form-field-wrap').addClass('customSwitchButton');
    $(document).find('span.tw-switch-group').parent().parent().addClass('tw-switch-field');
    kendoWindowOpen('#addSanctionModal');
    toggleFormDisableAttr('#addSanctionDetailsForm', '.add-sanction-submit');
});
$('body').on('click', '.editStudentSanctionBtn', function (e) {
    e.preventDefault();
    let id = $(this).attr('data-id');
    ajaxActionV2('api/get-student-sanction-data', 'POST', { id: id }, function (response) {
        let editSanctionDetailsForm = $('#editSanctionDetailsForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                layout: 'grid',
                type: 'group',
                grid: { cols: 6, gutter: 16 },
                items: [
                    {
                        field: 'type',
                        editor: 'RadioGroup',
                        label: 'Type',
                        colSpan: 6,
                        editorOptions: {
                            items: [
                                { value: '1', label: 'Academic' },
                                { value: '2', label: 'Payment' },
                            ],
                            layout: 'horizontal',
                        },
                        validation: { required: true },
                    },
                    {
                        field: 'comment',
                        label: 'Comment',
                        editor: 'TextArea',
                        colSpan: 6,
                        editorOptions: {
                            placeholder: 'comment',
                            rows: 4,
                        },
                        validation: {
                            required: { message: 'Enter Comment' },
                        },
                    },
                    {
                        field: 'is_show_msg',
                        editor: 'Switch',
                        label: 'Show message to Student',
                        colSpan: 6,
                        validation: { required: false },
                    },
                    {
                        field: 'is_active',
                        editor: 'Switch',
                        label: 'Set Sanction Status Active',
                        colSpan: 6,
                        validation: { required: false },
                    },
                    {
                        field: 'active_date',
                        editor: 'DatePicker',
                        label: 'Active From',
                        editorOptions: {
                            format: dateFormatFrontSideJS,
                        },
                        validation: { required: true },
                        colSpan: 6,
                    },
                ],
                buttonsTemplate: setWindowFooterTemplate('SAVE'),
                submit: function (ev) {
                    ev.preventDefault();
                    let dataArr = formValidateAndReturnFormData('#editSanctionDetailsForm');
                    dataArr['id'] = id;
                    if (dataArr) {
                        ajaxActionV2(
                            'api/update-student-sanction',
                            'POST',
                            dataArr,
                            function (response) {
                                notificationDisplay(response.message, '', response.status);
                                if (response.status == 'success') {
                                    closeKendoWindow('#editSanctionModal');
                                    reloadGrid('#studentSanctionGrid');
                                }
                            }
                        );
                    }
                    return false;
                },
            });
        editSanctionDetailsForm.data('kendoForm').setOptions({
            formData: {
                type: response.data.type,
                comment: response.data.comment,
                is_active: response.data.is_active == 1 ? true : false,
                is_show_msg: response.data.is_show_msg == 1 ? true : false,
                active_date: response.data.active_date,
            },
        });
        $(document).find('.k-form-field-wrap').addClass('customSwitchButton');
        kendoWindowOpen('#editSanctionModal');
    });
    $('#studentSanctionGrid').data('kendoTooltip').hide();
    $(document).find('span.tw-switch-group').parent().parent().addClass('tw-switch-field');
});
$('body').on('click', '#exportStudentSanctionBtn', function (e) {
    e.preventDefault();
    gridExportExcelData('#studentSanctionGrid', 'studentSanctionList');
});
$('body').on('click', '.deleteStudentSanctionBtn', function (e) {
    e.preventDefault();
    let primaryID = $(this).attr('data-id');
    $('#deleteStudentSanctionModal').data('kendoDialog').open();
    $('#deleteStudentSanctionModal').find('#deleteStudentSanctionId').val(primaryID);
});
$('body').on('click', '.viewExitInterViewBtn', function (e) {
    e.preventDefault();
    ajaxActionV2('api/get-course-tab-data', 'POST', selectedDataArr, function (response) {
        let responseData = response.data;
        let responseArr = {
            data: responseData.courseSummary.currentCourseSummary,
            getResultCalculationMethod: responseData.courseSummary.getResultCalculationMethod,
            studentDetails: responseData.courseSummary.studentDetails,
        };
        var profile_pic = '';
        if (responseData.courseSummary.studentProfile == '') {
            let name = responseData.courseSummary.studentDetails[0].full_name
                .toUpperCase()
                .split(/\s+/);
            let shortName =
                name.length >= 2 ? name[0].charAt(0) + name[1].charAt(0) : name[0].substring(0, 2);
            profile_pic =
                '<div class="rounded-full"><div class="flex user-profile-pic w-16 h-16 rounded-full bg-blue-500 items-center"><span class="text-2xl flex justify-center items-center leading-6 px-1 w-full">' +
                shortName +
                '</span></div></div>';
        } else {
            profile_pic =
                '<div class="w-16 h-16 rounded-full"><img class="w-16 h-16 rounded-full" src="' +
                responseData.courseSummary.studentProfile +
                '"/></div>';
        }
        $('.studentProfilePic').html(profile_pic);
        $('.studentProfilePicName').html(responseData.courseSummary.studentDetails[0].full_name);
        $('.studentStudId').html(responseData.courseSummary.studentDetails[0].generated_stud_id);
        $('#viewExitInterviewModal_wnd_title').text(
            'Exit Interview for ' + responseData.courseSummary.studentDetails[0].full_name
        );
        $(document)
            .find('.studentCourseDetailHeader')
            .html(kendo.template($('#studentCourseDetailHeaderTemplate').html())(responseArr));
    });
    initializeActionMenu('studentExitInterviewGrid', 'studentExitInterviewActionTemplate');
    $('#studentExitInterviewGrid').kendoGrid({
        dataSource: customDataSource(
            'api/get-student-exit-interview-list',
            {
                course_name: { type: 'string' },
                created_at: { type: 'date' },
                updated_at: { type: 'date' },
                exit_type: { type: 'string' },
                reason: { type: 'string' },
                reason: { type: 'string' },
                comment: { type: 'string' },
                new_college: { type: 'string' },
            },
            selectedDataArr
        ),
        pageable: customPageableArr(),
        sortable: true,
        resizable: true,
        columns: [
            {
                // template: "#if(course_name != '') {#<div class='customClass'>#:course_name#</div>#} else{ # <div>-</div>  #}#",
                template: function (dataItem) {
                    return manageCourseName(dataItem.course_name);
                },
                // template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'> # if(course_name == '') { # - #else{   }# </div>",
                field: 'course_name',
                title: 'COURSE NAME',
                sortable: true,
                width: 300,
            },
            {
                template:
                    "<div class='flex items-center text-13 leading-4 text-gray-600'> #: kendo.toString(created_at,'" +
                    dateFormatFrontSideJS +
                    "') # </div>",
                field: 'created_at',
                title: 'CREATED ON',
            },
            {
                template:
                    "<div class=' flex items-center text-13 leading-4 text-gray-600'>#: kendo.toString(updated_at,'" +
                    dateFormatFrontSideJS +
                    "') # </div>",
                field: 'updated_at',
                title: 'UPDATED ON',
            },
            {
                template: function (dataItem) {
                    return manageExitType(dataItem.exit_type);
                },
                field: 'exit_type',
                title: 'EXIT TYPE',
                sortable: true,
                width: 120,
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: reason #</div>",
                field: 'reason',
                title: 'REASON',
                sortable: true,
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: comment #</div>",
                field: 'comment',
                title: 'COMMENT',
                sortable: true,
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: new_college #</div>",
                field: 'new_college',
                title: 'COLLEGE NAME',
                sortable: true,
            },
            {
                template: function (dataItem) {
                    // return studentExitInterviewGridManageAction(dataItem.id);
                    return manageActionColumn(dataItem.id);
                },
                field: 'action',
                title: 'ACTION',
                width: 100,
            },
        ],
        noRecords: noRecordTemplate(),
        dataBound: function (e) {
            onBoundStudentExitInterview(e);
            togglePagination('#studentExitInterviewGrid');
        },
    });
    kendoWindowOpen('#viewExitInterviewModal');
});
$('body').on('click', '.addExitInterviewBtn', function (e) {
    window.headerDetail.headerDropDownCourseData.shift();
    e.preventDefault();
    let addExitInterviewDetailsForm = $('#addExitInterviewDetailsForm')
        .html('')
        .kendoForm({
            validatable: defaultErrorTemplate(),
            orientation: 'vertical',
            layout: 'grid',
            type: 'group',
            grid: { cols: 6, gutter: 16 },
            items: [
                {
                    field: 'exit_type',
                    editor: 'RadioGroup',
                    label: 'Exit Type',
                    colSpan: 6,
                    editorOptions: {
                        items: [
                            { value: '1', label: 'Course Exit' },
                            { value: '2', label: 'College Exit' },
                        ],
                        layout: 'horizontal',
                        change: function (e) {
                            if (e.newValue == '1') {
                                $('#course_id-form-label').closest('.k-form-field').show();
                            } else {
                                $('#course_id-form-label').closest('.k-form-field').hide();
                            }
                        },
                    },
                    validation: { required: true },
                },
                {
                    field: 'course_id',
                    label: 'Course Name',
                    editor: 'DropDownList',
                    colSpan: 6,
                    editorOptions: {
                        optionLabel: 'Select Course Name',
                        dataSource: getDropdownDataSource(
                            'get-all-student-course-list',
                            selectedDataArr
                        ),
                        dataTextField: 'Name',
                        dataValueField: 'Id',
                    },
                },
                {
                    field: 'select_collage',
                    editor: 'Switch',
                    label: 'New College',
                    attributes: {
                        class: 'tw-switch-group',
                    },
                    editorOptions: {
                        change: function (e) {
                            if (e.checked) {
                                $('#new_college-form-label').closest('.k-form-field').show();
                                $('#new_college').attr('required', 'required');
                            } else {
                                $('#new_college-form-label').closest('.k-form-field').hide();
                                $('#new_college').removeAttr('required');
                            }
                        },
                    },
                    colSpan: 6,
                },
                {
                    field: 'new_college',
                    label: 'New  College Name',
                    colSpan: 6,
                    validation: { required: true },
                },
                {
                    field: 'reason',
                    label: 'Reason',
                    validation: { required: true },
                    colSpan: 6,
                },
                {
                    field: 'comment',
                    label: 'Comment',
                    editor: 'TextArea',
                    colSpan: 6,
                    editorOptions: {
                        placeholder: 'comment',
                        rows: 4,
                    },
                    validation: { required: { message: 'Enter Comment' } },
                },
                {
                    field: 'is_comm_log',
                    editor: 'Switch',
                    label: 'Add to Student Communication Log',
                    colSpan: 6,
                    attributes: {
                        class: 'tw-switch-group',
                    },
                },
            ],
            buttonsTemplate: setWindowFooterTemplate(
                'SAVE',
                'submit',
                'add-exit-interview-submit',
                true
            ),
            submit: function (ev) {
                ev.preventDefault();
                let dataArr = formValidateAndReturnFormData('#addExitInterviewDetailsForm');
                if (dataArr) {
                    ajaxActionV2('api/save-exit-interview', 'POST', dataArr, function (response) {
                        notificationDisplay(response.message, '', response.status);
                        if (response.status == 'success') {
                            closeKendoWindow('#addExitInterviewModal');
                            reloadGrid('#studentExitInterviewGrid');
                        }
                    });
                }
                return false;
            },
        });
    addExitInterviewDetailsForm.data('kendoForm').setOptions({
        formData: {
            exit_type: 1,
            select_collage: true,
            is_comm_log: true,
        },
    });
    $(document).find('.k-form-field-wrap').addClass('customSwitchButton');
    $(document).find('span.tw-switch-group').parent().parent().addClass('tw-switch-field');
    kendoWindowOpen('#addExitInterviewModal');
    toggleFormDisableAttr('#addExitInterviewDetailsForm', '.add-exit-interview-submit');
});
$('body').on('click', '.editStudentExitInterviewBtn', function (e) {
    window.headerDetail.headerDropDownCourseData.shift();
    $('#studentExitInterviewGrid').data('kendoTooltip').hide();
    let primaryID = $(this).attr('data-id');
    selectedDataArr.id = primaryID;
    e.preventDefault();
    ajaxActionV2(
        'api/get-student-exit-interview-detail',
        'POST',
        selectedDataArr,
        function (response) {
            let editExitInterviewDetailsForm = $('#editExitInterviewDetailsForm')
                .html('')
                .kendoForm({
                    validatable: defaultErrorTemplate(),
                    orientation: 'vertical',
                    layout: 'grid',
                    type: 'group',
                    grid: { cols: 6, gutter: 16 },
                    items: [
                        {
                            field: 'exit_type',
                            editor: 'RadioGroup',
                            label: 'Exit Type',
                            colSpan: 6,
                            editorOptions: {
                                items: [
                                    { value: '1', label: 'Course Exit' },
                                    { value: '2', label: 'College Exit' },
                                ],
                                layout: 'horizontal',
                                change: function (e) {
                                    if (e.newValue == '1') {
                                        $('#course_id-form-label').closest('.k-form-field').show();
                                    } else {
                                        $('#course_id-form-label').closest('.k-form-field').hide();
                                    }
                                },
                            },
                            // validation: { required: true },
                            attributes: { disabled: true },
                        },
                        {
                            field: 'course_id',
                            label: 'Course Name',
                            editor: 'DropDownList',
                            colSpan: 6,
                            attributes: { disabled: true },

                            editorOptions: {
                                optionLabel: 'Select Course Name',
                                dataSource: getDropdownDataSource(
                                    'get-all-student-course-list',
                                    selectedDataArr
                                ),
                                dataTextField: 'Name',
                                dataValueField: 'Id',
                            },
                        },
                        {
                            field: 'select_collage',
                            editor: 'Switch',
                            label: 'New College',
                            attributes: {
                                class: 'tw-switch-group',
                            },
                            attributes: { disabled: true },

                            editorOptions: {
                                change: function (e) {
                                    if (e.checked) {
                                        $('#new_college-form-label')
                                            .closest('.k-form-field')
                                            .show();
                                        $('#new_college').attr('required', 'required');
                                    } else {
                                        $('#new_college-form-label')
                                            .closest('.k-form-field')
                                            .hide();
                                        $('#new_college').removeAttr('required');
                                    }
                                },
                            },
                            colSpan: 6,
                        },
                        {
                            field: 'new_college',
                            label: 'New  College Name',
                            colSpan: 6,
                            // validation: { required: true },
                            attributes: { disabled: true },
                        },
                        {
                            field: 'reason',
                            label: 'Reason',
                            validation: { required: true },
                            colSpan: 6,
                        },
                        {
                            field: 'comment',
                            label: 'Comment',
                            editor: 'TextArea',
                            colSpan: 6,
                            editorOptions: {
                                placeholder: 'comment',
                                rows: 4,
                            },
                            validation: {
                                required: { message: 'Enter Comment' },
                            },
                        },
                        {
                            field: 'is_comm_log',
                            editor: 'Switch',
                            label: 'Add to Student Communication Log',
                            colSpan: 6,
                            attributes: {
                                class: 'tw-switch-group',
                            },
                        },
                    ],
                    buttonsTemplate: setWindowFooterTemplate('SAVE'),
                    submit: function (ev) {
                        ev.preventDefault();
                        let disabledFields = $(document)
                            .find('#editExitInterviewDetailsForm')
                            .find('[disabled]');
                        disabledFields.prop('disabled', false);
                        let dataArr = formValidateAndReturnFormData(
                            '#editExitInterviewDetailsForm'
                        );
                        disabledFields.prop('disabled', true);
                        dataArr['id'] = response.data.id;
                        if (dataArr) {
                            ajaxActionV2(
                                'api/update-exit-interview',
                                'POST',
                                dataArr,
                                function (response) {
                                    notificationDisplay(response.message, '', response.status);
                                    if (response.status == 'success') {
                                        closeKendoWindow('#editExitInterviewModal');
                                        reloadGrid('#studentExitInterviewGrid');
                                    }
                                }
                            );
                        }
                        return false;
                    },
                });
            setTimeout(function () {
                $('#editExitInterviewDetailsForm')
                    .find('#exit_type')
                    .getKendoRadioGroup()
                    .enable(false);
                if (response.data.exit_type == 1) {
                    $('#editExitInterviewDetailsForm')
                        .find('#course_id-form-label')
                        .closest('.k-form-field')
                        .show();
                } else {
                    $('#editExitInterviewDetailsForm')
                        .find('#course_id-form-label')
                        .closest('.k-form-field')
                        .hide();
                }
            }, 500);

            editExitInterviewDetailsForm.data('kendoForm').setOptions({
                formData: {
                    exit_type: response.data.exit_type,
                    course_id: response.data.course_id,
                    select_collage: response.data.new_college ? true : false,
                    new_college: response.data.new_college,
                    reason: response.data.reason,
                    comment: response.data.comment,
                    is_comm_log: true,
                },
            });
            if (response.data.new_college == '') {
                $('#new_college-form-label').closest('.k-form-field').hide();
            }
            $(document).find('.k-form-field-wrap').addClass('customSwitchButton');
            $(document).find('span.tw-switch-group').parent().parent().addClass('tw-switch-field');
            kendoWindowOpen('#editExitInterviewModal');
        }
    );
});
$('body').on('click', '#exportStudentExitInterviewBtn', function (e) {
    e.preventDefault();
    gridExportExcelDataV2('#studentExitInterviewGrid', 'studentExitInterviewList');
});
$('body').on('click', '.deleteStudentExitInterviewBtn', function (e) {
    e.preventDefault();
    let primaryID = $(this).attr('data-id');
    $('#studentExitInterviewGrid').data('kendoTooltip').hide();
    $('#deleteStudentExitInterviewModal').data('kendoDialog').open();
    $('#deleteStudentExitInterviewModal').find('#deleteStudentExitInterviewId').val(primaryID);
});
$('body').on('click', '.studentCardPdfBtn', function (e) {
    window.location = site_url + 'api/get-student-card/' + selectedDataArr.student_id;
});
$('body').on('click', '.studentCardPdfBtn2', function (e) {
    e.preventDefault();

    // Check if student is selected
    if (!selectedDataArr || !selectedDataArr.student_id) {
        notificationDisplay('Please select a student first.', '', 'error');
        return;
    }

    // Show loading indicator
    kendo.ui.progress($(document.body), true);

    // Generate student ID card using new system
    window.location = site_url + 'api/get-student-card-new/' + selectedDataArr.student_id;

    // Hide loading indicator after a short delay
    setTimeout(function() {
        kendo.ui.progress($(document.body), false);
    }, 2000);
});
$('body').on('click', '.resetPasswordBtn', function (e) {
    e.preventDefault();
    let primaryID = $(this).attr('data-id');
    $('.studentName').text($('#getStudentName').text());
    $('#resetPasswordModal').data('kendoDialog').open();
    // $("#resetPasswordModal").find("#deleteStudentExitInterviewId").val(primaryID);
});
$('body').on('click', '.reSendActivationBtn', function (e) {
    e.preventDefault();
    let primaryID = $(this).attr('data-id');
    $('.studentName').text($('#getStudentName').text());
    $('#reSendActivationModal').data('kendoDialog').open();
});
$('body').on('click', '.viewStudentTCSIBtn', function (e) {
    e.preventDefault();
    loadTcsiInformationForm();
});
$('body').on('click', '.studentInterventionBtn', function (e) {
    e.preventDefault();
    let gteUrl = site_url + 'compliance-intervention/' + studentId;
    window.open(gteUrl, '_blank');
});
$('body').on('click', '.dismissNotificationBtn', function () {
    $('#' + $(this).data('target')).hide();
});
$('body').on('click', '.gteDashboardBtn', function (e) {
    e.preventDefault();
    let gteUrl = site_url + 'gte-dashboard/' + studentId + '/' + selectedStudCourseID;
    window.open(gteUrl, '_blank');
});
$('body').on('click', '.saveDisabilityInformationBtn', function (e) {
    let dataArr = formValidateAndReturnFormData('#disabilityFormDiv');
    if (dataArr) {
        ajaxActionV2('api/save-tcsi-disability-info', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                // closeKendoWindow("#viewStudentTCSIModal");
            }
        });
    }
});
$('body').on('click', '.addTcsiOfferCreditBtn', function (e) {
    e.preventDefault();
    kendoWindowOpen(addTcsiCreditOfferModalId);
});
$('body').on('click', '.editTcsiCreditOfferBtn', function (e) {
    e.preventDefault();
    let primaryID = $(this).attr('data-id');
    $(tcsiCreditOfferGridId).data('kendoTooltip').hide();

    if (primaryID > 0) {
        ajaxActionV2(
            'api/get-tcsi-credit-offer-info',
            'POST',
            { id: primaryID },
            function (response) {
                if (response.status == 'success') {
                    kendoWindowOpen(editTcsiCreditOfferModalId);
                    let res = response.data;
                    let editTcsiCreditOfferForm = $(document).find('#editTcsiCreditOfferForm');
                    editTcsiCreditOfferForm.find('#creditOfferId').val(res.id);
                    editTcsiCreditOfferForm
                        .find('#edit_student_course_id')
                        .data('kendoDropDownList')
                        .value(res.student_course_id);
                    editTcsiCreditOfferForm
                        .find('#edit_credit_used_value')
                        .val(res.credit_used_value);
                    editTcsiCreditOfferForm
                        .find('#edit_credit_basis_code')
                        .data('kendoDropDownList')
                        .value(res.credit_basis_code);
                    editTcsiCreditOfferForm
                        .find('#edit_credit_provider_code')
                        .data('kendoDropDownList')
                        .value(res.credit_provider_code);
                } else {
                    notificationDisplay(response.message, '', response.status);
                }
            }
        );
    }
});
$('body').on('click', '.deleteTcsiCreditOfferBtn', function (e) {
    e.preventDefault();
    let primaryID = $(this).attr('data-id');
    $(tcsiCreditOfferGridId).data('kendoTooltip').hide();
    $(deleteTcsiCreditOfferModalId).data('kendoDialog').open();
    $(deleteTcsiCreditOfferModalId).find('#deleteStudTcsiCreditOfferId').val(primaryID);
});
