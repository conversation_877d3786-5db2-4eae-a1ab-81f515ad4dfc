<?php

namespace App\Constants\Menu;

class SadminMenu
{
    public const SADMIN_MENU_ITEMS = [
        [
            'label' => 'Dashboard',
            'url' => '/admin-dashboard',
            'mainmenu' => ['clients'],
            'activeurls' => ['admin-dashboard', 'settings'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
        ],
        [
            'label' => 'Task Management',
            'url' => '#',
            'mainmenu' => ['clients'],
            'activeurls' => ['task-list', 'task-template'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 13L9 17L19 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Tasks',
                    'url' => '/task-list',
                ],
                [
                    'label' => 'Task Template',
                    'url' => '/task-template',
                ],
            ],
        ],
        [
            'label' => 'Task Management Beta',
            'url' => '#',
            'mainmenu' => ['clients'],
            'activeurls' => [],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 13L9 17L19 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
        ],
        // [
        //     'label' => 'Document',
        //     'url' => '#',

        //     'mainmenu' => ['clients'],
        //     'activeurls' => ['view-college-document', 'subject_specific', 'course_specific', 'list-collegematerials'],
        //     'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        //     <path d="M7 21H17C18.1046 21 19 20.1046 19 19V9.41421C19 9.149 18.8946 8.89464 18.7071 8.70711L13.2929 3.29289C13.1054 3.10536 12.851 3 12.5858 3H7C5.89543 3 5 3.89543 5 5V19C5 20.1046 5.89543 21 7 21Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        //     </svg>',
        //     'sub_menu' => [
        //         [
        //             'label' => 'College Documents',
        //             'url' => '/view-college-document/0',
        //             'subactiveurls' => ['view-college-document']
        //         ],
        //         [
        //             'label' => 'Subject Specific',
        //             'url' => '/subject_specific/0',
        //             'subactiveurls' => ['subject_specific']
        //         ],
        //         [
        //             'label' => 'Course Specific',
        //             'url' => '/course_specific/0',
        //             'subactiveurls' => ['course_specific']
        //         ],
        //         [
        //             'label' => 'College Materials',
        //             'url' => '/list-collegematerials/0',
        //             'subactiveurls' => ['list-collegematerials']
        //         ],
        //     ],
        // ],
        [
            'label' => 'Document',
            'url' => '/spa/miscellaneous/document',
            'gap_after' => true,
            'mainmenu' => ['clients'],
            'activeurls' => ['spa.miscellaneous.document'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 21H17C18.1046 21 19 20.1046 19 19V9.41421C19 9.149 18.8946 8.89464 18.7071 8.70711L13.2929 3.29289C13.1054 3.10536 12.851 3 12.5858 3H7C5.89543 3 5 3.89543 5 5V19C5 20.1046 5.89543 21 7 21Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
        ],
        [
            'label' => 'Application',
            'url' => '#',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'student-online-application',
                'student-continue-online-application',
                'apply-online',
                'apply-online-step2',
                'apply-online-step3',
                'apply-online-step4',
                'apply-online-confirmation',
                'apply-online-final-step',
                'apply-online-step-thank-you',
                'offer-manage',
                'list-student-document',
                'offer-manage-add-course',
                'offer-manage-communication-log',
                'offer-manage-checklist',
                'offer-manage-document',
                'offer-manage-confirmation',
                'offer-manage-edit',
                'offer-manage-send-mail',
                'offer-manage-upfront-fee-schedule',
                'student-offer-checklist',
                'manage-offer',
                'gte-dashboard',
                'offer-mail',
                'apply-short-course',
                'apply-short-course-step-thank-you',
                'apply-short-course-step2',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12H15M9 16H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L18.7071 8.70711C18.8946 8.89464 19 9.149 19 9.41421V19C19 20.1046 18.1046 21 17 21Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'New Online Application',
                    'url' => '/student-online-application',
                ],
                [
                    'label' => 'Continue a Saved Online Application',
                    'url' => '/student-continue-online-application',
                ],
                [
                    'label' => 'Manage Offers',
                    'url' => '/offer-manage',
                    'subactiveurls' => [
                        'list-student-document',
                        'offer-manage-add-course',
                        'offer-manage-communication-log',
                        'offer-manage-checklist',
                        'offer-manage-document',
                        'offer-manage-confirmation',
                        'offer-manage-edit',
                        'offer-manage-send-mail',
                        'offer-manage-upfront-fee-schedule',
                        'student-offer-checklist',
                    ],
                ],
                [
                    'label' => 'Manage Offers beta',
                    'url' => '/manage-offer',
                    'subactiveurls' => ['gte-dashboard'],
                ],
                [
                    'label' => 'Apply Short Course',
                    'url' => '/apply-short-course',
                    'subactiveurls' => [
                        'apply-short-course-step2',
                        'apply-short-course-step-thank-you',
                    ],
                ],
                [
                    'label' => 'Offer Mailing List',
                    'url' => '/offer-mail',
                ],
            ],
        ],
        // [
        //     'label' => 'Students',
        //     'url' => '/search-students',
        //     'mainmenu' => ['clients'],
        //     'activeurls' => [
        //         'search-students',
        //         'student-profile',
        //         'student-profile-edit',
        //         'student-saved-application',
        //         'student-send-sms',
        //         'student-profile-send-mail',
        //         'student-course-information',
        //         'student-course-add',
        //         'student-course-edit',
        //         'student-send-letter',
        //         'student-edit-service-information',
        //         'student-subject-enrollment',
        //         'student-training-plan',
        //         'student-course-checklist',
        //         'student-course-communication',
        //         'student-course-upfront-fee',
        //         'student-course-offer-checklist',
        //         'student-course-deferral',
        //         'student-flexible-timetable',
        //         'edit-student-course-deferral',
        //         'student-intervention',
        //         'edit-student-intervention',
        //         'student-edit-diary',
        //         'student-communication-log',
        //         'add-student-sanction',
        //         'edit-student-sanction',
        //         'combine-payment-schedule',
        //         'student-payment-summary',
        //         'add-student-sanction',
        //         'edit-student-sanction',
        //         'student-service-payment-add',
        //         'student-oshc',
        //         'student-interview',
        //         'add-student-sanction',
        //         'edit-student-sanction',
        //         'student-training-plan',
        //         'student-vet-fee-help-add',
        //         'student-vet-fee-help-list',
        //         'student-claim-tracking-list'
        //     ],
        //     'svgicon' => '<svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        //     <path d="M3.79688 7.40625C3.45833 7.30208 3.28906 7.04167 3.28906 6.625C3.28906 6.20833 3.45833 5.94792 3.79688 5.84375L11.375 3.57812C11.5833 3.52604 11.8047 3.5 12.0391 3.5C12.2734 3.5 12.4948 3.52604 12.7031 3.57812L20.2812 5.84375C20.6198 5.94792 20.7891 6.20833 20.7891 6.625C20.7891 7.04167 20.6198 7.30208 20.2812 7.40625L17.2344 8.30469C17.5208 8.98177 17.6641 9.67188 17.6641 10.375C17.6641 11.9375 17.1172 13.2656 16.0234 14.3594C14.9297 15.4531 13.6016 16 12.0391 16C10.4766 16 9.14844 15.4531 8.05469 14.3594C6.96094 13.2656 6.41406 11.9375 6.41406 10.375C6.41406 9.67188 6.55729 8.98177 6.84375 8.30469L5.00781 7.75781V8.96875C5.32031 9.15104 5.47656 9.41146 5.47656 9.75C5.47656 10.0625 5.33333 10.3229 5.04688 10.5312L5.63281 12.9531C5.65885 13.0573 5.65885 13.1484 5.63281 13.2266C5.63281 13.3047 5.60677 13.3698 5.55469 13.4219C5.5026 13.474 5.4375 13.5 5.35938 13.5H3.71875C3.61458 13.5 3.52344 13.4479 3.44531 13.3438C3.39323 13.2135 3.39323 13.0833 3.44531 12.9531L4.03125 10.5312C3.74479 10.3229 3.60156 10.0625 3.60156 9.75C3.60156 9.41146 3.75781 9.15104 4.07031 8.96875V7.48438L3.79688 7.40625ZM12.0391 14.125C13.0807 14.125 13.9661 13.7604 14.6953 13.0312C15.4245 12.3021 15.7891 11.4167 15.7891 10.375C15.7891 9.85417 15.6719 9.34635 15.4375 8.85156L12.7031 9.67188C12.2604 9.80208 11.8177 9.80208 11.375 9.67188L8.64062 8.85156C8.40625 9.34635 8.28906 9.85417 8.28906 10.375C8.28906 11.4167 8.65365 12.3021 9.38281 13.0312C10.112 13.7604 10.9974 14.125 12.0391 14.125ZM11.9219 5.375L7.74219 6.625L11.9219 7.875C12 7.875 12.0781 7.875 12.1562 7.875L16.3359 6.625L12.1562 5.375C12.0781 5.375 12 5.375 11.9219 5.375ZM15.75 16.0391C17.1562 16.0911 18.3411 16.625 19.3047 17.6406C20.2943 18.6302 20.7891 19.8281 20.7891 21.2344V21.625C20.7891 22.1458 20.6068 22.5885 20.2422 22.9531C19.8776 23.3177 19.4349 23.5 18.9141 23.5H5.16406C4.64323 23.5 4.20052 23.3177 3.83594 22.9531C3.47135 22.5885 3.28906 22.1458 3.28906 21.625V21.2344C3.28906 19.8281 3.77083 18.6302 4.73438 17.6406C5.72396 16.625 6.92188 16.0911 8.32812 16.0391L12.0391 19.125L15.75 16.0391ZM11.1016 21.625V20.7656L7.74219 17.9922C6.98698 18.1745 6.36198 18.5651 5.86719 19.1641C5.39844 19.763 5.16406 20.4531 5.16406 21.2344V21.625H11.1016ZM18.9141 21.625V21.2344C18.9141 20.4531 18.6667 19.763 18.1719 19.1641C17.7031 18.5651 17.0911 18.1745 16.3359 17.9922L12.9766 20.7656V21.625H18.9141Z" fill="currentColor"/>
        //     </svg>'
        // ],
        [
            'label' => 'Students',
            'url' => '/search-student-scout',
            'mainmenu' => ['clients'],
            'activeurls' => ['search-student-scout', 'search-student', 'student-profile-view'],
            'svgicon' => '<svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3.79688 7.40625C3.45833 7.30208 3.28906 7.04167 3.28906 6.625C3.28906 6.20833 3.45833 5.94792 3.79688 5.84375L11.375 3.57812C11.5833 3.52604 11.8047 3.5 12.0391 3.5C12.2734 3.5 12.4948 3.52604 12.7031 3.57812L20.2812 5.84375C20.6198 5.94792 20.7891 6.20833 20.7891 6.625C20.7891 7.04167 20.6198 7.30208 20.2812 7.40625L17.2344 8.30469C17.5208 8.98177 17.6641 9.67188 17.6641 10.375C17.6641 11.9375 17.1172 13.2656 16.0234 14.3594C14.9297 15.4531 13.6016 16 12.0391 16C10.4766 16 9.14844 15.4531 8.05469 14.3594C6.96094 13.2656 6.41406 11.9375 6.41406 10.375C6.41406 9.67188 6.55729 8.98177 6.84375 8.30469L5.00781 7.75781V8.96875C5.32031 9.15104 5.47656 9.41146 5.47656 9.75C5.47656 10.0625 5.33333 10.3229 5.04688 10.5312L5.63281 12.9531C5.65885 13.0573 5.65885 13.1484 5.63281 13.2266C5.63281 13.3047 5.60677 13.3698 5.55469 13.4219C5.5026 13.474 5.4375 13.5 5.35938 13.5H3.71875C3.61458 13.5 3.52344 13.4479 3.44531 13.3438C3.39323 13.2135 3.39323 13.0833 3.44531 12.9531L4.03125 10.5312C3.74479 10.3229 3.60156 10.0625 3.60156 9.75C3.60156 9.41146 3.75781 9.15104 4.07031 8.96875V7.48438L3.79688 7.40625ZM12.0391 14.125C13.0807 14.125 13.9661 13.7604 14.6953 13.0312C15.4245 12.3021 15.7891 11.4167 15.7891 10.375C15.7891 9.85417 15.6719 9.34635 15.4375 8.85156L12.7031 9.67188C12.2604 9.80208 11.8177 9.80208 11.375 9.67188L8.64062 8.85156C8.40625 9.34635 8.28906 9.85417 8.28906 10.375C8.28906 11.4167 8.65365 12.3021 9.38281 13.0312C10.112 13.7604 10.9974 14.125 12.0391 14.125ZM11.9219 5.375L7.74219 6.625L11.9219 7.875C12 7.875 12.0781 7.875 12.1562 7.875L16.3359 6.625L12.1562 5.375C12.0781 5.375 12 5.375 11.9219 5.375ZM15.75 16.0391C17.1562 16.0911 18.3411 16.625 19.3047 17.6406C20.2943 18.6302 20.7891 19.8281 20.7891 21.2344V21.625C20.7891 22.1458 20.6068 22.5885 20.2422 22.9531C19.8776 23.3177 19.4349 23.5 18.9141 23.5H5.16406C4.64323 23.5 4.20052 23.3177 3.83594 22.9531C3.47135 22.5885 3.28906 22.1458 3.28906 21.625V21.2344C3.28906 19.8281 3.77083 18.6302 4.73438 17.6406C5.72396 16.625 6.92188 16.0911 8.32812 16.0391L12.0391 19.125L15.75 16.0391ZM11.1016 21.625V20.7656L7.74219 17.9922C6.98698 18.1745 6.36198 18.5651 5.86719 19.1641C5.39844 19.763 5.16406 20.4531 5.16406 21.2344V21.625H11.1016ZM18.9141 21.625V21.2344C18.9141 20.4531 18.6667 19.763 18.1719 19.1641C17.7031 18.5651 17.0911 18.1745 16.3359 17.9922L12.9766 20.7656V21.625H18.9141Z" fill="currentColor"/>
            </svg>',
        ],
        [
            'label' => 'Orientation',
            'url' => '/student-orientation',
            'gap_after' => true,
            'mainmenu' => ['clients'],
            'activeurls' => ['student-orientation'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 21V5C19 3.89543 18.1046 3 17 3H7C5.89543 3 5 3.89543 5 5V21M19 21L21 21M19 21H14M5 21L3 21M5 21H10M9 6.99998H10M9 11H10M14 6.99998H15M14 11H15M10 21V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V21M10 21H14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
        ],
        [
            'label' => 'Cohorts',
            'url' => '#',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'compliance-intake-group',
                'compliance-studentassign-group',
                'compliance-studentassign-group-management',
                'flexible-timetable-allocation-by-group',
                'student-bulk-enrollment-by-group',
                'bulk-enrollment-by-subject',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 4.35418C12.7329 3.52375 13.8053 3 15 3C17.2091 3 19 4.79086 19 7C19 9.20914 17.2091 11 15 11C13.8053 11 12.7329 10.4762 12 9.64582M15 21H3V20C3 16.6863 5.68629 14 9 14C12.3137 14 15 16.6863 15 20V21ZM15 21H21V20C21 16.6863 18.3137 14 15 14C13.9071 14 12.8825 14.2922 12 14.8027M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Setup Intake Group',
                    'url' => '/compliance-intake-group',
                ],
                [
                    'label' => 'Assign Student to Group',
                    'url' => '/compliance-studentassign-group',
                    'subactiveurls' => ['compliance-studentassign-group-management'],
                ],
                [
                    'label' => 'Bulk Enrollment by Group',
                    'url' => '/student-bulk-enrollment-by-group',
                ],
                [
                    'label' => 'Flexible Timetable Allocation by Group',
                    'url' => '/flexible-timetable-allocation-by-group',
                ],
                [
                    'label' => 'Bulk Enrollment by Subject',
                    'url' => '/bulk-enrollment-by-subject',
                ],
            ],
        ],
        [
            'label' => 'Timetable',
            'url' => '#',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'manage-timetable',
                'default-timetable',
                'edit-class-timetable',
                'manage-assessment-group',
                'timetable-report',
                'view-timetable',
                'timetable-replacement-teacher',
                'attendance-list-by-class',
                'attendance-list-for-bulk-pdf',
                'attendance-list-by-individual-class',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 6.25C21 4.45507 19.5449 3 17.75 3H6.25C4.45507 3 3 4.45507 3 6.25V17.75C3 19.5449 4.45507 21 6.25 21H12.0218C11.7253 20.5368 11.4858 20.0335 11.3135 19.5H6.25C5.2835 19.5 4.5 18.7165 4.5 17.75V8.5H19.5V11.3135C20.0335 11.4858 20.5368 11.7253 21 12.0218V6.25ZM6.25 4.5H17.75C18.7165 4.5 19.5 5.2835 19.5 6.25V7H4.5V6.25C4.5 5.2835 5.2835 4.5 6.25 4.5ZM23 17.5C23 14.4624 20.5376 12 17.5 12C14.4624 12 12 14.4624 12 17.5C12 20.5376 14.4624 23 17.5 23C20.5376 23 23 20.5376 23 17.5ZM17.5 17.5H19.5C19.7761 17.5 20 17.7239 20 18C20 18.2762 19.7761 18.5 19.5 18.5H17C16.7268 18.5 16.5048 18.2809 16.5001 18.0089L16.5 17.9999V14.9999C16.5 14.7238 16.7239 14.4999 17 14.4999C17.2761 14.4999 17.5 14.7238 17.5 14.9999L17.5 17.5Z" fill="currentColor"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Class Timetable',
                    'url' => '/manage-timetable',
                    'subactiveurls' => ['default-timetable', 'edit-class-timetable'],
                ],
                [
                    'label' => 'Assessment Group',
                    'url' => '/manage-assessment-group',
                ],
                [
                    'label' => 'Timetable Report',
                    'url' => '/timetable-report',
                ],
                [
                    'label' => 'View Timetable',
                    'url' => '/view-timetable',
                ],
                [
                    'label' => 'Replacement Teacher',
                    'url' => '/timetable-replacement-teacher',
                ],
                [
                    'label' => 'Print Attendance List',
                    'url' => '/attendance-list-by-class',
                    'subactiveurls' => ['attendance-list-for-bulk-pdf', 'attendance-list-by-individual-class'],
                ],
            ],
        ],
        [
            'label' => 'Timetable Beta',
            'url' => '/timetable-beta',
            'mainmenu' => ['clients'],
            'activeurls' => ['timetable-beta'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 6.25C21 4.45507 19.5449 3 17.75 3H6.25C4.45507 3 3 4.45507 3 6.25V17.75C3 19.5449 4.45507 21 6.25 21H12.0218C11.7253 20.5368 11.4858 20.0335 11.3135 19.5H6.25C5.2835 19.5 4.5 18.7165 4.5 17.75V8.5H19.5V11.3135C20.0335 11.4858 20.5368 11.7253 21 12.0218V6.25ZM6.25 4.5H17.75C18.7165 4.5 19.5 5.2835 19.5 6.25V7H4.5V6.25C4.5 5.2835 5.2835 4.5 6.25 4.5ZM23 17.5C23 14.4624 20.5376 12 17.5 12C14.4624 12 12 14.4624 12 17.5C12 20.5376 14.4624 23 17.5 23C20.5376 23 23 20.5376 23 17.5ZM17.5 17.5H19.5C19.7761 17.5 20 17.7239 20 18C20 18.2762 19.7761 18.5 19.5 18.5H17C16.7268 18.5 16.5048 18.2809 16.5001 18.0089L16.5 17.9999V14.9999C16.5 14.7238 16.7239 14.4999 17 14.4999C17.2761 14.4999 17.5 14.7238 17.5 14.9999L17.5 17.5Z" fill="currentColor"/>
            </svg>',
        ],
        [
            'label' => 'Attendance',
            'url' => '#',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'attendance-summary',
                'add-class-attendance',
                'student-attendance-summary',
                'bulk-attendance-weekly',
                'bulk-attendance-subject',
                'report-and-warnings',
                'student-mail-waring-setting',
                'student-mail-percentage-waring-setting',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.3135 15.4999C11.4859 14.9665 11.7253 14.4631 12.0219 13.9999H4.25278C3.01076 13.9999 2.00391 15.0068 2.00391 16.2488V16.8265C2.00391 17.7193 2.32242 18.5828 2.90219 19.2617C4.46849 21.0959 6.8545 22.0011 10.0004 22.0011C10.9314 22.0011 11.7961 21.9218 12.5927 21.7626C12.2335 21.3494 11.9256 20.8903 11.6789 20.3954C11.1555 20.4658 10.5962 20.5011 10.0004 20.5011C7.26206 20.5011 5.29618 19.7553 4.04287 18.2876C3.69502 17.8802 3.50391 17.3622 3.50391 16.8265V16.2488C3.50391 15.8352 3.83919 15.4999 4.25278 15.4999H11.3135ZM10.0004 2.00464C12.7618 2.00464 15.0004 4.24321 15.0004 7.00464C15.0004 9.76606 12.7618 12.0046 10.0004 12.0046C7.23894 12.0046 5.00036 9.76606 5.00036 7.00464C5.00036 4.24321 7.23894 2.00464 10.0004 2.00464ZM10.0004 3.50464C8.06737 3.50464 6.50036 5.07164 6.50036 7.00464C6.50036 8.93764 8.06737 10.5046 10.0004 10.5046C11.9334 10.5046 13.5004 8.93764 13.5004 7.00464C13.5004 5.07164 11.9334 3.50464 10.0004 3.50464ZM17.5 12C20.5376 12 23 14.4624 23 17.5C23 20.5376 20.5376 23 17.5 23C14.4624 23 12 20.5376 12 17.5C12 14.4624 14.4624 12 17.5 12ZM19.5 17.5001H17.5L17.5 14.9999C17.5 14.7238 17.2761 14.4999 17 14.4999C16.7239 14.4999 16.5 14.7238 16.5 14.9999L16.5 17.9985L16.5 18.0001C16.5 18.2762 16.7239 18.5001 17 18.5001H19.5C19.7761 18.5001 20 18.2762 20 18.0001C20 17.7239 19.7761 17.5001 19.5 17.5001Z" fill="currentColor"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Attendance Summary',
                    'url' => '/attendance-summary',
                    'subactiveurls' => ['student-attendance-summary'],
                ],
                [
                    'label' => 'Add Class Attendance (Daily/Weekly)',
                    'url' => '/add-class-attendance',
                ],
                [
                    'label' => 'Bulk Attendance',
                    'url' => '/bulk-attendance-weekly',
                    'subactiveurls' => ['bulk-attendance-subject'],
                ],
                [
                    'label' => 'Reports',
                    'url' => '/report-and-warnings',
                ],
                [
                    'label' => 'Warning Days setting',
                    'url' => '/student-mail-waring-setting',
                ],
                [
                    'label' => 'Warning Percentage Setting',
                    'url' => '/student-mail-percentage-waring-setting',
                ],
            ],
        ],
        [
            'label' => 'Competency',
            'url' => '#',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'setup-assessment-task',
                'task-entry',
                'task-results-entry',
                'transfer-results',
                'vocational-placement-result',
                'transfer-results-by-unit',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.15186 3.01151C7.9835 2.3641 9.07148 2.00586 10.1527 2.00586C10.7992 2.00586 11.3227 2.22796 11.7193 2.58073C11.8266 2.6762 11.9219 2.77874 12.0066 2.88524C12.0913 2.77874 12.1866 2.6762 12.2939 2.58073C12.6904 2.22796 13.214 2.00586 13.8605 2.00586C14.9417 2.00586 16.0297 2.3641 16.8613 3.01151C17.5083 3.5152 18.0155 4.20952 18.2142 5.04938C18.6344 5.11884 19.0078 5.33301 19.3097 5.61631C19.7904 6.06746 20.1323 6.71923 20.3477 7.39005C20.5662 8.07099 20.6756 8.83647 20.6426 9.57125C20.6257 9.94742 20.5706 10.3308 20.4651 10.6979C20.4871 10.7074 20.509 10.7173 20.5308 10.7275C20.901 10.9021 21.2002 11.1754 21.4253 11.5381C21.8504 12.2228 22 13.2094 22 14.4663C22 15.9108 21.4479 16.8917 20.7377 17.5009C20.3256 17.8544 19.8708 18.0735 19.4681 18.1905C19.3286 18.8831 18.9797 19.6636 18.449 20.3276C17.7262 21.2319 16.6027 21.9961 15.092 21.9961C13.8817 21.9961 12.929 21.3268 12.317 20.6851C12.2051 20.5677 12.1014 20.4482 12.0066 20.3293C11.9117 20.4482 11.8081 20.5677 11.6961 20.6851C11.0842 21.3268 10.1314 21.9961 8.92113 21.9961C7.41046 21.9961 6.287 21.2319 5.5642 20.3276C5.03345 19.6636 4.68458 18.8831 4.54508 18.1905C4.14239 18.0735 3.68758 17.8544 3.27551 17.5009C2.56532 16.8917 2.01318 15.9108 2.01318 14.4663C2.01318 13.2094 2.16274 12.2228 2.58785 11.5381C2.81299 11.1754 3.11222 10.9021 3.48238 10.7275C3.50418 10.7173 3.52608 10.7074 3.54808 10.6979C3.44257 10.3308 3.38752 9.94742 3.37061 9.57125C3.33759 8.83647 3.44696 8.07099 3.66553 7.39005C3.88086 6.71923 4.22275 6.06746 4.70353 5.61631C5.00543 5.33301 5.37882 5.11884 5.79898 5.04938C5.9977 4.20952 6.50484 3.5152 7.15186 3.01151ZM8.07328 4.19514C7.52752 4.62 7.20795 5.18596 7.20795 5.83756C7.20795 6.07868 7.09203 6.3051 6.89641 6.44605C6.70078 6.58701 6.44932 6.62532 6.22059 6.54901C6.08319 6.50317 5.93859 6.51435 5.72995 6.71013C5.49872 6.92712 5.2633 7.32029 5.09376 7.84849C4.92746 8.36658 4.84439 8.95421 4.8691 9.5039C4.89412 10.0605 5.02647 10.5215 5.23478 10.8342C5.26963 10.8866 5.2973 10.9423 5.31771 11H6.39981C7.90396 11 9.13596 12.1653 9.24235 13.6423C9.97936 13.9371 10.5 14.6577 10.5 15.5C10.5 16.6046 9.60457 17.5 8.5 17.5C7.39543 17.5 6.5 16.6046 6.5 15.5C6.5 14.6663 7.01014 13.9517 7.7353 13.6514C7.63924 13 7.07791 12.5 6.39981 12.5H3.7998C3.7914 12.5 3.78302 12.4999 3.77468 12.4996C3.62541 12.8476 3.51318 13.449 3.51318 14.4663C3.51318 15.4872 3.88474 16.0472 4.25219 16.3625C4.65542 16.7084 5.09811 16.798 5.22636 16.798C5.64058 16.798 5.97636 17.1338 5.97636 17.548C5.97636 17.9671 6.20816 18.7308 6.73591 19.3911C7.2447 20.0276 7.96863 20.4961 8.92113 20.4961C9.55821 20.4961 10.145 20.1381 10.6106 19.6499C10.8374 19.4121 11.0135 19.1651 11.129 18.9647C11.1869 18.8643 11.2243 18.7847 11.2449 18.7319L11.2501 18.7183V16.2629C11.25 16.2586 11.25 16.2543 11.25 16.25C11.25 16.2457 11.25 16.2414 11.2501 16.2371L11.2501 9.24999H10.3546C10.0579 9.98295 9.33935 10.5 8.5 10.5C7.39543 10.5 6.5 9.60457 6.5 8.5C6.5 7.39543 7.39543 6.5 8.5 6.5C9.33934 6.5 10.0579 7.01704 10.3546 7.74999H11.2501V5.2236L11.2499 5.21226C11.2496 5.20084 11.249 5.18149 11.2476 5.15538C11.2448 5.10293 11.239 5.02446 11.227 4.92895C11.2027 4.73404 11.1552 4.48751 11.0669 4.25168C10.9773 4.01271 10.8608 3.82469 10.7222 3.70141C10.5992 3.59193 10.43 3.50586 10.1527 3.50586C9.38659 3.50586 8.62717 3.76396 8.07328 4.19514ZM12.7631 17V18.7183L12.7682 18.7319C12.7889 18.7847 12.8263 18.8643 12.8842 18.9647C12.9996 19.1651 13.1757 19.4121 13.4025 19.6499C13.8682 20.1381 14.455 20.4961 15.092 20.4961C16.0446 20.4961 16.7685 20.0276 17.2773 19.3911C17.805 18.7308 18.0368 17.9671 18.0368 17.548C18.0368 17.1338 18.3726 16.798 18.7868 16.798C18.9151 16.798 19.3578 16.7084 19.761 16.3625C20.1284 16.0472 20.5 15.4872 20.5 14.4663C20.5 13.2579 20.3417 12.6364 20.151 12.3293C20.0682 12.196 19.9826 12.1274 19.8909 12.0842C19.7894 12.0363 19.6378 12 19.4026 12C19.1261 12 18.872 11.8478 18.7414 11.604C18.6109 11.3602 18.6251 11.0644 18.7784 10.8342C18.9867 10.5215 19.1191 10.0605 19.1441 9.5039C19.1688 8.95421 19.0857 8.36658 18.9194 7.84849C18.7499 7.32029 18.5145 6.92712 18.2832 6.71013C18.0746 6.51435 17.93 6.50317 17.7926 6.54901C17.5639 6.62532 17.3124 6.58701 17.1168 6.44605C16.9212 6.3051 16.8052 6.07868 16.8052 5.83756C16.8052 5.18596 16.4857 4.62 15.9399 4.19514C15.386 3.76396 14.6266 3.50586 13.8605 3.50586C13.5832 3.50586 13.414 3.59193 13.291 3.70141C13.1524 3.82469 13.0359 4.01271 12.9463 4.25168C12.858 4.48751 12.8105 4.73404 12.7862 4.92895C12.7742 5.02446 12.7684 5.10293 12.7656 5.15538C12.7642 5.18149 12.7636 5.20084 12.7633 5.21226L12.7631 5.2236L12.7631 15.5H13.4C14.1456 15.5 14.75 14.8956 14.75 14.15V12.3546C14.017 12.0579 13.5 11.3393 13.5 10.5C13.5 9.39543 14.3954 8.5 15.5 8.5C16.6046 8.5 17.5 9.39543 17.5 10.5C17.5 11.3393 16.983 12.0579 16.25 12.3546V14.15C16.25 15.724 14.974 17 13.4 17H12.7631ZM8.5 8C8.22386 8 8 8.22386 8 8.5C8 8.77614 8.22386 9 8.5 9C8.77614 9 9 8.77614 9 8.5C9 8.22386 8.77614 8 8.5 8ZM8 15.5C8 15.7761 8.22386 16 8.5 16C8.77614 16 9 15.7761 9 15.5C9 15.2239 8.77614 15 8.5 15C8.22386 15 8 15.2239 8 15.5ZM15 10.5C15 10.7761 15.2239 11 15.5 11C15.7761 11 16 10.7761 16 10.5C16 10.2239 15.7761 10 15.5 10C15.2239 10 15 10.2239 15 10.5Z" fill="currentColor"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Setup Assessment Task',
                    'url' => '/setup-assessment-task',
                ],
                [
                    'label' => 'Task Entry',
                    'url' => '/task-entry',
                ],
                [
                    'label' => 'Task Results Entry',
                    'url' => '/task-results-entry',
                ],
                [
                    'label' => 'Transfer Results',
                    'url' => '/transfer-results',
                ],
                [
                    'label' => 'Vocational Placement Result',
                    'url' => '/vocational-placement-result',
                ],
                [
                    'label' => 'Transfer Results by Unit',
                    'url' => '/transfer-results-by-unit',
                ],
            ],
        ],
        [
            'label' => 'Certificate',
            'url' => '#',
            'mainmenu' => ['clients'],
            'activeurls' => ['generate-bulk-certificate', 'spa.certificate.templates'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20.543 6.69531C20.8711 7.02344 21.0352 7.42188 21.0352 7.89062V19.5625C21.0352 20.0312 20.8711 20.4297 20.543 20.7578C20.2148 21.0859 19.8164 21.25 19.3477 21.25H10.9102V19.5625H19.3477V9.4375H15.6914C15.457 9.4375 15.2578 9.35547 15.0938 9.19141C14.9297 9.02734 14.8477 8.82812 14.8477 8.59375V4.9375H9.22266V7.75H7.53516V4.9375C7.53516 4.63281 7.60547 4.35156 7.74609 4.09375C7.91016 3.83594 8.12109 3.63672 8.37891 3.49609C8.63672 3.33203 8.91797 3.25 9.22266 3.25H16.3945C16.8633 3.25 17.2617 3.41406 17.5898 3.74219L20.543 6.69531ZM16.5352 7.75H19.207L16.5352 5.07812V7.75ZM11.7188 15.1328C11.4609 15.4141 11.3086 15.5898 11.2617 15.6602C11.2383 15.7305 11.1797 15.9531 11.0859 16.3281C10.9688 16.7031 10.7227 16.9492 10.3477 17.0664C10.043 17.1367 9.85547 17.1953 9.78516 17.2422V21.25L7.53516 20.125L5.28516 21.25V17.2422C5.21484 17.1953 5.02734 17.1367 4.72266 17.0664C4.34766 16.9492 4.10156 16.7031 3.98438 16.3281C3.89062 15.9531 3.82031 15.7305 3.77344 15.6602C3.75 15.5898 3.60938 15.4141 3.35156 15.1328C3.07031 14.8516 2.97656 14.5117 3.07031 14.1133C3.16406 13.7617 3.21094 13.5508 3.21094 13.4805C3.21094 13.3867 3.16406 13.1523 3.07031 12.7773C2.97656 12.3789 3.07031 12.0391 3.35156 11.7578C3.60938 11.5 3.75 11.3359 3.77344 11.2656C3.82031 11.1953 3.89062 10.9727 3.98438 10.5977C4.10156 10.1992 4.34766 9.95312 4.72266 9.85938C4.95703 9.78906 5.08594 9.75391 5.10938 9.75391C5.15625 9.75391 5.23828 9.71875 5.35547 9.64844C5.47266 9.57812 5.54297 9.53125 5.56641 9.50781C5.61328 9.46094 5.71875 9.35547 5.88281 9.19141C6.16406 8.91016 6.49219 8.81641 6.86719 8.91016C7.21875 9.00391 7.44141 9.05078 7.53516 9.05078C7.62891 9.05078 7.85156 9.00391 8.20312 8.91016C8.57812 8.81641 8.90625 8.91016 9.1875 9.19141C9.44531 9.44922 9.60938 9.60156 9.67969 9.64844C9.77344 9.69531 9.99609 9.76562 10.3477 9.85938C10.7227 9.95312 10.9688 10.1992 11.0859 10.5977C11.1797 10.9727 11.2383 11.1953 11.2617 11.2656C11.3086 11.3359 11.4609 11.5 11.7188 11.7578C12 12.0391 12.0938 12.3789 12 12.7773C11.9062 13.1523 11.8594 13.3867 11.8594 13.4805C11.8594 13.5508 11.9062 13.7617 12 14.1133C12.0938 14.5117 12 14.8516 11.7188 15.1328ZM5.91797 14.9922C6.36328 15.4141 6.90234 15.625 7.53516 15.625C8.16797 15.625 8.69531 15.4141 9.11719 14.9922C9.5625 14.5469 9.78516 14.0078 9.78516 13.375C9.78516 12.7422 9.5625 12.2148 9.11719 11.793C8.69531 11.3477 8.16797 11.125 7.53516 11.125C6.90234 11.125 6.36328 11.3477 5.91797 11.793C5.49609 12.2148 5.28516 12.7422 5.28516 13.375C5.28516 14.0078 5.49609 14.5469 5.91797 14.9922Z" fill="currentColor"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Certificate Templates',
                    'url' => '/spa/certificate/templates',
                ],
                [
                    'label' => 'Generate Bulk Certificates',
                    'url' => '/generate-bulk-certificate',
                ],
            ],
        ],
        [
            'label' => 'Intervention',
            'url' => '/compliance-intervention',
            'gap_after' => true,
            'mainmenu' => ['clients'],
            'activeurls' => ['compliance-intervention', 'edit-student-intervention', 'add-intervention'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M20.25 5c-2.663 0-5.258-.943-7.8-2.85a.75.75 0 0 0-.9 0C9.008 4.057 6.413 5 3.75 5a.75.75 0 0 0-.75.75V11c0 5.001 2.958 8.676 8.725 10.948a.75.75 0 0 0 .55 0C18.042 19.676 21 16 21 11V5.75a.75.75 0 0 0-.75-.75M4.5 6.478c2.577-.152 5.08-1.09 7.5-2.8c2.42 1.71 4.923 2.648 7.5 2.8V11c0 4.256-2.453 7.379-7.5 9.442C6.953 18.379 4.5 15.256 4.5 11zm8.243 1.174a.75.75 0 0 0-1.493.102v6.498l.007.102a.75.75 0 0 0 1.493-.102V7.754zM12 18a1 1 0 1 0 0-2a1 1 0 0 0 0 2"/></svg>',
        ],
        [
            'label' => 'Payment',
            'url' => '#',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'generate-invoice',
                'student-account',
                'student-payment-summary',
                'student-pay-schedule-fee',
                'pay-update-invoice',
                'student-payment-checklist',
                'student-agent-commission',
                'student-refund-history',
                'student-transfer-payment',
                'student-scholarship',
                'student-service-payment-add',
                'student-service-payment-edit',
                'student-service-paymentv2',
                'student-miscellaneous-payment',
                'student-service-payment',
                'student-initial-payment',
                'student-communication-log',
                'combine-payment-schedule',
                'failed-transactions',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.8434 9.65538C14.2053 10.0725 14.8369 10.1173 15.254 9.7553C15.6712 9.39335 15.7159 8.76176 15.354 8.34462L13.8434 9.65538ZM10.1567 14.3446C9.79471 13.9275 9.16313 13.8827 8.74599 14.2447C8.32885 14.6067 8.28411 15.2382 8.64607 15.6554L10.1567 14.3446ZM13 7C13 6.44772 12.5523 6 12 6C11.4477 6 11 6.44772 11 7H13ZM11 17C11 17.5523 11.4477 18 12 18C12.5523 18 13 17.5523 13 17L11 17ZM20 12C20 16.4183 16.4183 20 12 20V22C17.5228 22 22 17.5228 22 12H20ZM12 20C7.58172 20 4 16.4183 4 12H2C2 17.5228 6.47715 22 12 22V20ZM4 12C4 7.58172 7.58172 4 12 4V2C6.47715 2 2 6.47715 2 12H4ZM12 4C16.4183 4 20 7.58172 20 12H22C22 6.47715 17.5228 2 12 2V4ZM12 11C11.3415 11 10.7905 10.8202 10.4334 10.5822C10.0693 10.3394 10 10.1139 10 10H8C8 10.9907 8.6023 11.7651 9.32398 12.2463C10.0526 12.732 11.0017 13 12 13V11ZM10 10C10 9.8861 10.0693 9.66058 10.4334 9.41784C10.7905 9.17976 11.3415 9 12 9V7C11.0017 7 10.0526 7.26796 9.32398 7.75374C8.6023 8.23485 8 9.00933 8 10H10ZM12 9C12.9038 9 13.563 9.33231 13.8434 9.65538L15.354 8.34462C14.5969 7.47209 13.3171 7 12 7V9ZM12 13C12.6585 13 13.2095 13.1798 13.5666 13.4178C13.9308 13.6606 14 13.8861 14 14H16C16 13.0093 15.3977 12.2348 14.676 11.7537C13.9474 11.268 12.9983 11 12 11V13ZM11 7V8H13V7H11ZM11 16L11 17L13 17L13 16L11 16ZM12 15C11.0962 15 10.437 14.6677 10.1567 14.3446L8.64607 15.6554C9.40317 16.5279 10.683 17 12 17L12 15ZM14 14C14 14.1139 13.9308 14.3394 13.5666 14.5822C13.2095 14.8202 12.6586 15 12 15V17C12.9983 17 13.9474 16.732 14.676 16.2463C15.3977 15.7651 16 14.9907 16 14H14ZM11 8L11 16L13 16L13 8L11 8Z" fill="currentColor"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Payment',
                    'url' => '/student-account',
                    'subactiveurls' => [
                        'student-payment-summary',
                        'student-pay-schedule-fee',
                        'pay-update-invoice',
                        'student-payment-checklist',
                        'student-agent-commission',
                        'student-refund-history',
                        'student-transfer-payment',
                        'student-scholarship',
                        'student-service-payment-add',
                        'student-service-payment-edit',
                        'student-service-paymentv2',
                        'student-miscellaneous-payment',
                        'student-service-payment',
                        'student-initial-payment',
                        'student-communication-log',
                        'combine-payment-schedule',
                    ],
                ],
                [
                    'label' => 'Invoice',
                    'url' => '/generate-invoice',
                ],
                [
                    'label' => 'Xero Failed Transaction',
                    'url' => '/failed-transactions',
                ],
            ],
        ],
        /*  [
            'label' => 'Others',
            'url' => '#',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'verify-student-usi',
                'create-student-usi',
                'locate-student-usi',
                'aus-key-config',
                'communication',
                'view-register-improvement',
                'add-register-improvement',
                'edit-register-improvement',
                'student-placement',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 20L11 4M13 20L17 4M6 9H20M4 15H18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'USI1',
                    'url' => '/verify-student-usi',
                    'subactiveurls' => ['create-student-usi', 'locate-student-usi', 'aus-key-config']
                ],
                [
                    'label' => 'Communications',
                    'url' => '/communication/all',
                    'subactiveurls' => ['communication']
                ],
                [
                    'label' => 'Continuous Improvement',
                    'url' => '/view-register-improvement',
                    'subactiveurls' => ['add-register-improvement', 'edit-register-improvement']
                ],
                [
                    'label' => 'Placement',
                    'url' => '/student-placement',
                ]
            ],
        ], */
        [
            'label' => 'USI',
            'url' => '/spa/usi-verifications',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'verify-student-usi',
            ],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M19.5 6.25v5.063a6.5 6.5 0 0 1 1.5.709V6.25A3.25 3.25 0 0 0 17.75 3H6.25A3.25 3.25 0 0 0 3 6.25v11.5A3.25 3.25 0 0 0 6.25 21h5.772a6.5 6.5 0 0 1-.709-1.5H6.25a1.75 1.75 0 0 1-1.75-1.75V6.25c0-.966.784-1.75 1.75-1.75h11.5c.966 0 1.75.784 1.75 1.75M11 17.5q0 .216.014.43C8.474 17.556 7 15.755 7 14v-.5A1.5 1.5 0 0 1 8.5 12h5.534A6.5 6.5 0 0 0 11 17.5m1-12a2.75 2.75 0 1 1 0 5.5a2.75 2.75 0 0 1 0-5.5m11 12a5.5 5.5 0 1 1-11 0a5.5 5.5 0 0 1 11 0m-2.146-2.354a.5.5 0 0 0-.708 0L16.5 18.793l-1.646-1.647a.5.5 0 0 0-.708.708l2 2a.5.5 0 0 0 .708 0l4-4a.5.5 0 0 0 0-.708" />
                            </svg>',
        ],
        [
            'label' => 'Communications',
            'url' => '/communication/all',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'communication',
                'communication/all',
            ],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M17.383 15.5a2.2 2.2 0 0 1-.231-1.5h-10.9a2.25 2.25 0 0 0-2.248 2.249v.578c0 .892.318 1.756.898 2.435c1.566 1.834 3.952 2.74 7.098 2.74q.585 0 1.133-.043a2.26 2.26 0 0 1-.008-1.503q-.54.045-1.125.045c-2.738 0-4.704-.746-5.957-2.213a2.25 2.25 0 0 1-.54-1.462v-.577a.75.75 0 0 1 .75-.75zM12 2.005a5 5 0 1 1 0 10a5 5 0 0 1 0-10m0 1.5a3.5 3.5 0 1 0 0 7a3.5 3.5 0 0 0 0-7m6.192 10.49l.476-1.205c.242-.614.92-.933 1.548-.728l.431.141c.724.237 1.326.806 1.35 1.569c.1 3.11-2.476 7.583-5.213 9.055c-.673.362-1.468.123-2.035-.391l-.337-.305a1.253 1.253 0 0 1-.142-1.706l.8-1.01c.29-.367.767-.53 1.22-.42l1.292.313q1.654-1.095 1.774-3.079l-.917-.964a1.2 1.2 0 0 1-.247-1.27" />
                        </svg>',
        ],
        [

            'label' => 'Continuous Improvement',
            'url' => '/view-register-improvement',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'view-register-improvement',
                'add-register-improvement',
                'edit-register-improvement',
            ],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M16.749 2h4.554l.1.014l.099.028l.06.026q.12.052.219.15l.04.044l.044.057l.054.09l.039.09l.019.064l.014.064l.009.095v4.532a.75.75 0 0 1-1.493.102l-.007-.102V4.559l-6.44 6.44a.75.75 0 0 1-.976.073L13 11L9.97 8.09l-5.69 5.689a.75.75 0 0 1-1.133-.977l.073-.084l6.22-6.22a.75.75 0 0 1 .976-.072l.084.072l3.03 2.91L19.438 3.5h-2.69a.75.75 0 0 1-.742-.648l-.007-.102a.75.75 0 0 1 .648-.743zM3.75 17a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75m5.75-3.25a.75.75 0 0 0-1.5 0v7.5a.75.75 0 0 0 1.5 0zM13.75 15a.75.75 0 0 1 .75.75v5.5a.75.75 0 0 1-1.5 0v-5.5a.75.75 0 0 1 .75-.75m5.75-4.25a.75.75 0 0 0-1.5 0v10.5a.75.75 0 0 0 1.5 0z" />
                            </svg>',
        ],
        [

            'label' => 'Placement',
            'url' => '/student-placement',
            'mainmenu' => ['clients'],
            'activeurls' => [
                'student-placement',
            ],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M2 7.25A3.25 3.25 0 0 1 5.25 4h13.5A3.25 3.25 0 0 1 22 7.25v7.25c0-1.19-.593-2.24-1.5-2.873V7.25a1.75 1.75 0 0 0-1.75-1.75H5.25A1.75 1.75 0 0 0 3.5 7.25v9.5c0 .966.784 1.75 1.75 1.75h8.058A2.76 2.76 0 0 0 13 19.772v.103q0 .063.002.125H5.25A3.25 3.25 0 0 1 2 16.75zM18.5 17a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5m0 6c3.214 0 4.5-1.569 4.5-3.125v-.103c0-.979-.794-1.772-1.773-1.772h-5.454c-.98 0-1.773.793-1.773 1.772v.103C14 21.437 15.286 23 18.5 23M7.75 8a.75.75 0 0 0-.75.75v6.5a.75.75 0 0 0 1.5 0V13.5h1.25a2.75 2.75 0 1 0 0-5.5zm2 4H8.5V9.5h1.25a1.25 1.25 0 1 1 0 2.5" />
                            </svg>',
        ],

        [
            'label' => 'Trainers List',
            'url' => '/view-teacher',
            'mainmenu' => ['trainers'],
            'activeurls' => ['view-teacher', 'teacher-profile', 'add-teacher', 'update-teacher-profile', 'teacher-profile-send-mail', 'teacher-send-sms', 'teacher-profile-matrix', 'teacher-timetable', 'teacher-communication-log', 'edit-teacher-communication-log', 'list-staff-document', 'teacher-leave-info'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                        </svg>',
        ],
        [
            'label' => 'Assign-Units',
            'url' => '/view-teacher-matrix',
            'mainmenu' => ['trainers'],
            'activeurls' => ['view-teacher-matrix', 'add-teacher-matrix', 'edit-teacher-matrix'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                        </svg>',
        ],
        [
            'label' => 'Timesheet',
            'url' => '#',
            'mainmenu' => ['trainers'],
            'activeurls' => ['generate-pay-period', 'timesheet-approved', 'assign-supervisor', 'timesheet-submission', 'timesheet-approval'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                        </svg>',

            'sub_menu' => [
                [
                    'label' => 'Pay Period',
                    'url' => '/generate-pay-period',
                ],
                [
                    'label' => 'Pay Period Beta',
                    'url' => '/spa/generate-pay-period',
                ],
                [
                    'label' => 'Manage Timesheet',
                    'url' => '/timesheet-approval',
                ],
                [
                    'label' => 'Manage Timesheet Beta',
                    'url' => '/spa/timesheet-approval',
                ],
                [
                    'label' => 'Submit Timesheet',
                    'url' => '/timesheet-submission',
                ],
                [
                    'label' => 'Submit Timesheet Beta',
                    'url' => '/spa/timesheet-submission',
                ],
                [
                    'label' => 'Approved Timesheet',
                    'url' => '/timesheet-approved',
                ],
                [
                    'label' => 'Approved Timesheet Beta',
                    'url' => '/spa/timesheet-approved',
                ],
                [
                    'label' => 'Supervisor',
                    'url' => '/assign-supervisor',
                ],
                [
                    'label' => 'Supervisor Beta',
                    'url' => '/spa/assign-supervisor',
                ],
            ],
        ],
        [
            'label' => 'Traineeship - Apprenticeship',
            'url' => '/trineeship-visits',
            'mainmenu' => ['trainers'],
            'activeurls' => ['trineeship-visits'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                        </svg>',
        ],
        [
            'label' => 'Traineeship - Apprenticeship Beta',
            'url' => '/spa/traineeship-visit',
            'mainmenu' => ['trainers'],
            'activeurls' => ['traineeship-visit'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                        </svg>',
        ],
        [
            'label' => 'Setup Services',
            'url' => '/services-setup',
            'mainmenu' => ['partners'],
            'activeurls' => ['services-setup'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                        </svg>',
        ],
        [
            'label' => 'Service Providers',
            'url' => '/provider-setup',
            'mainmenu' => ['partners'],
            'activeurls' => ['provider-setup', 'provider-setup-add', 'provider-setup-edit'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                        </svg>',
        ],
        [
            'label' => 'Asign-facility',
            'url' => '/provider-facility',
            'mainmenu' => ['partners'],
            'activeurls' => ['provider-facility'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                          </svg>',
        ],
        [
            'label' => 'Update Service Allocation',
            'url' => '/service-request-allocation',
            'mainmenu' => ['partners'],
            'activeurls' => ['service-request-allocation'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>',
        ],
        [
            'label' => 'Provider Payment',
            'url' => '/provider-payment-v2',
            'mainmenu' => ['partners'],
            'activeurls' => ['provider-payment-v2'],
            'svgicon' => '<svg width="24" height="24"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    aria-hidden="true">
                                <path stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="1.5"
                                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>',
        ],
        [
            'label' => 'Agent List',
            'url' => '/view-agent-list',
            'mainmenu' => ['partner_agent'],
            'activeurls' => ['view-agent-list', 'list-agent-document', 'edit-agent', 'agent-communication-add-data'],
            'svgicon' => '<svg width="24" height="24"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true">
           <path stroke-linecap="round"
                 stroke-linejoin="round"
                 stroke-width="1.5"
                 d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
             </svg>',
        ],
        [
            'label' => 'Add Agent',
            'url' => '/add-agent',
            'mainmenu' => ['partner_agent'],
            'activeurls' => ['add-agent'],
            'svgicon' => '<svg width="24" height="24"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                   viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true">
                  <path stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                  d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>',
        ],
        [
            'label' => 'Agent Mailing List',
            'url' => '/view-agent-email-list',
            'mainmenu' => ['partner_agent'],
            'activeurls' => ['view-agent-email-list'],
            'svgicon' => '<svg class=" text-primary-blue-100 text-gray-400 h-6 w-6"
             xmlns="http://www.w3.org/2000/svg"
             fill="none"
             viewBox="0 0 24 24"
             stroke="currentColor"
             aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5M3 10l6.75 4.5M21 10l-6.75 4.5m0 0l-1.14.76a2 2 0 01-2.22 0l-1.14-.76" />
        </svg>',
        ],
        [
            'label' => 'Agent Payment',
            'url' => '/view-agent-payment-list',
            'mainmenu' => ['partner_agent'],
            'activeurls' => ['view-agent-payment-list', 'payment-history', 'process-commission-agent', 'credit-bonus-allocation'],
            'svgicon' => '  <svg width="24" height="24"
             xmlns="http://www.w3.org/2000/svg"
             fill="none"
             viewBox="0 0 24 24"
             stroke="currentColor"
             aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        </svg>',
        ],
        [
            'label' => 'Agent Commission',
            'url' => '/agent-commission-add',
            'mainmenu' => ['partner_agent'],
            'activeurls' => ['agent-commission-add', 'edit-agent-commission'],
            'svgicon' => '<svg width="24" height="24"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        </svg>',
        ],
        [
            'label' => 'Agent Communication Log',
            'url' => '/agent-communication-add',
            'mainmenu' => ['partner_agent'],
            'activeurls' => ['agent-communication-add', 'agent-communication-edit-data'],
            'svgicon' => '<svg width="24" height="24"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
        </svg>',
        ],
        [
            'label' => 'Employer List',
            'url' => '/manage-employer',
            'mainmenu' => ['partner_employee'],
            'activeurls' => ['manage-employer', 'add-employer', 'edit-employer'],
            'svgicon' => '<svg width="24" height="24"
                             xmlns="http://www.w3.org/2000/svg"
                             fill="none"
                             viewBox="0 0 24 24"
                             stroke="currentColor"
                             aria-hidden="true">
                        <path stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="1.5"
                              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>',
        ],
        [
            'label' => 'Employer Beta',
            'url' => '/spa/manage-employer',
            'mainmenu' => ['partner_employee'],
            'activeurls' => [],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                          </svg>',
        ],
        [
            'label' => 'Employer Invoice',
            'url' => '/employer-invoice',
            'mainmenu' => ['partner_employee'],
            'activeurls' => ['employer-invoice'],
            'svgicon' => '<svg width="24" height="24"
                             xmlns="http://www.w3.org/2000/svg"
                             fill="none"
                             viewBox="0 0 24 24"
                             stroke="currentColor"
                             aria-hidden="true">
                        <path stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="1.5"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>',
        ],
        //        [
        //            'label' => 'Organisation Info',
        //            'url' => '/college-info',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['college-info'],
        //            'svgicon' => '<svg class=" text-gray-400  h-6 w-6"
        //                                                         xmlns="http://www.w3.org/2000/svg"
        //                                                         fill="none"
        //                                                         viewBox="0 0 24 24"
        //                                                         stroke="currentColor"
        //                                                         aria-hidden="true">
        //                                                        <path stroke-linecap="round"
        //                                                              stroke-linejoin="round"
        //                                                              stroke-width="1.5"
        //                                                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        //                                                    </svg>'
        //        ],
        //        [
        //            'label' => 'Course Type',
        //            'url' => '/course-type',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['course-type'],
        //            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                                stroke-linejoin="round"
        //                                                stroke-width="1.5"
        //                                                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        //                                </svg>'
        //        ],
        //        [
        //            'label' => 'Enrollment Fee',
        //            'url' => '/college-enrollment-fees-view',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['college-enrollment-fees-list', 'college-enrollment-fees'],
        //            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        //                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        //            </svg>'
        //        ],
        //        [
        //            'label' => 'Overseas Student Health Coverage',
        //            'url' => '/oshc-info',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['oshc-info', 'add-oshc', 'import-oshc'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Student Services Information',
        //            'url' => '/list-added-service-fee',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['list-added-service-fee', 'add-added-service-fee', 'edit-added-service-fee'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Letter of Offer - Checklist',
        //            'url' => '/offer-document-checklist',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['offer-document-checklist', 'add-offer-document-checklist', 'edit-offer-document-checklist'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Agents Document - Checklist',
        //            'url' => '/agent-document-checklist',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['agent-document-checklist', 'add-agent-document-checklist', 'edit-agent-document-checklist'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Agent Status',
        //            'url' => '/agent-status',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['agent-status', 'add-agent-status', 'edit-agent-status'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Country List',
        //            'url' => '/country-view',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['country-view', 'add-country', 'edit-country'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Language List',
        //            'url' => '/language-view',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['language-view', 'add-language'],
        //            'svgicon' => '
        //            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        //            <path d="M1 3H13M7 1V3M8.04819 12.5C6.52083 10.9178 5.28073 9.05645 4.41187 7M10.5 16H17.5M9 19L14 9L19 19M10.7511 3C9.78307 8.77022 6.06969 13.6095 1 16.129"  stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        //            </svg>
        //            '
        //        ],
        //        [
        //            'label' => 'Client ID Format',
        //            'url' => '/student-id-formate',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['student-id-formate'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Certificate ID format',
        //            'url' => '/certificate-id-formate-add',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['certificate-id-formate-add'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Competency - Grade',
        //            'url' => '/result-grade',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['result-grade', 'add-result-grade', 'edit-result-grade'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Intervention Strategy',
        //            'url' => '/intervention-strategy',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['intervention-strategy', 'add-intervention-strategy'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Manage Sections',
        //            'url' => '/setup-sections',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['setup-sections'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Setup Checklist',
        //            'url' => '/checklist-view',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['checklist-view', 'add-checklist', 'edit-checklist'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Extend Assessment Due Date',
        //            'url' => '/assessment-due-date-extention',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['assessment-due-date-extention'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Notification Setting',
        //            'url' => '/agent-email-template-setting',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['agent-email-template-setting'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        //                            </svg>'
        //        ],
        //        [
        //            'label' => 'Letter Setting',
        //            'url' => '/letter-setting',
        //            'mainmenu' => ['organisation_setup'],
        //            'activeurls' => ['letter-setting'],
        //            'svgicon' => '<svg width="24" height="24"
        //                                 xmlns="http://www.w3.org/2000/svg"
        //                                 fill="none"
        //                                 viewBox="0 0 24 24"
        //                                 stroke="currentColor"
        //                                 aria-hidden="true">
        //                                <path stroke-linecap="round"
        //                                      stroke-linejoin="round"
        //                                      stroke-width="1.5"
        //                                      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        //                            </svg>'
        //        ],
        [
            'label' => 'Staff List',
            'url' => '/staff-list',
            'mainmenu' => ['staff_setting'],
            'activeurls' => ['staff-list', 'staff-communication-log', 'add-staff', 'mail-staff', 'edit-staff'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M12.5 10.25a.75.75 0 0 1 .75-.75h3.5a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1-.75-.75m.75 4.75a.75.75 0 1 0 0 1.5h3.5a.75.75 0 1 0 0-1.5zm-2.47-5.22a.75.75 0 1 0-1.06-1.06l-1.47 1.47l-.47-.47a.75.75 0 0 0-1.06 1.06l1 1a.75.75 0 0 0 1.06 0zm0 4.44a.75.75 0 0 1 0 1.06l-2 2a.75.75 0 0 1-1.06 0l-1-1a.75.75 0 1 1 1.06-1.06l.47.47l1.47-1.47a.75.75 0 0 1 1.06 0m5.214-10.136A2.25 2.25 0 0 0 13.75 2h-3.5a2.25 2.25 0 0 0-2.236 2H6.25A2.25 2.25 0 0 0 4 6.25v13.5A2.25 2.25 0 0 0 6.25 22h11.5A2.25 2.25 0 0 0 20 19.75V6.25A2.25 2.25 0 0 0 17.75 4h-1.764zm0 .012L16 4.25q0-.078-.005-.154M10.25 6.5h3.5c.78 0 1.467-.397 1.871-1h2.129a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H6.25a.75.75 0 0 1-.75-.75V6.25a.75.75 0 0 1 .75-.75h2.129c.404.603 1.091 1 1.871 1m0-3h3.5a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1 0-1.5" />
</svg>',
        ],
        [
            'label' => 'Setup Permission for Staff',
            'url' => '/create-permission-group',
            'mainmenu' => ['staff_setting'],
            'activeurls' => ['create-permission-group', 'studen-agent-permission-setup', 'set-user-permission', 'page-permission-setup', 'edit-student-agent-permission'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M12 2q.465 0 .908.082a6.5 6.5 0 0 0-1.582 1.483A3.5 3.5 0 0 0 8.62 7.91a.75.75 0 0 1-.194.725L3.5 13.561V16.5h2v-1.75a.75.75 0 0 1 .75-.75H8.5v-1.25a.75.75 0 0 1 .22-.53l1.915-1.915q.336.7.823 1.298L10 13.06v1.689a.75.75 0 0 1-.75.75H7v1.75a.75.75 0 0 1-.75.75h-3.5a.75.75 0 0 1-.75-.75v-4a.75.75 0 0 1 .22-.53l4.855-4.855A5 5 0 0 1 12 2m4.5 1.5a4 4 0 0 0-1.894 7.524a.75.75 0 0 1 .394.66v7.734l2.017 1.834l2.123-1.799l-.921-.924a.75.75 0 0 1 0-1.058l.967-.971l-.967-.97A.75.75 0 0 1 18 15v-3.316a.75.75 0 0 1 .394-.66A4 4 0 0 0 16.5 3.5m-5.5 4a5.5 5.5 0 1 1 8.5 4.61v2.58l1.276 1.28a.75.75 0 0 1 0 1.06l-.967.97l.967.97a.75.75 0 0 1-.046 1.102l-3.245 2.75a.75.75 0 0 1-.99-.017l-2.75-2.5a.75.75 0 0 1-.245-.555v-7.64A5.5 5.5 0 0 1 11 7.5m6.5-1a1 1 0 1 1-2 0a1 1 0 0 1 2 0" />
</svg>',
        ],
        [
            'label' => 'Evaluation',
            'url' => '/admin-evaluation',
            'mainmenu' => ['staff_setting'],
            'activeurls' => ['admin-evaluation'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M15.986 4a2.25 2.25 0 0 0-2.236-2h-3.5a2.25 2.25 0 0 0-2.236 2H6.25A2.25 2.25 0 0 0 4 6.25v3.85a5.5 5.5 0 0 1 1.5-.51V6.25a.75.75 0 0 1 .75-.75h2.129c.404.603 1.091 1 1.871 1h3.5c.78 0 1.467-.397 1.871-1h2.129a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75h-3.918a1.76 1.76 0 0 1 0 1.5h3.918A2.25 2.25 0 0 0 20 19.75V6.25A2.25 2.25 0 0 0 17.75 4zm.009.096L16 4.25q0-.078-.005-.154M10.25 3.5h3.5a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1 0-1.5m-.3 14.39a4.5 4.5 0 1 0-1.145.976l2.915 2.914a.75.75 0 1 0 1.06-1.06zM6.5 18a3 3 0 1 1 0-6a3 3 0 0 1 0 6" />
</svg>',
        ],
        [
            'label' => 'Leave Info',
            'url' => '/view-leave-list',
            'mainmenu' => ['staff_setting'],
            'activeurls' => ['view-leave-list'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M12 1.998c5.524 0 10.002 4.478 10.002 10.002c0 5.523-4.478 10-10.002 10S2 17.523 2 12C1.999 6.476 6.476 1.998 12 1.998m0 1.5a8.502 8.502 0 1 0 0 17.003a8.502 8.502 0 0 0 0-17.003m-.004 7a.75.75 0 0 1 .744.648l.007.102l.004 5.502a.75.75 0 0 1-1.494.102l-.006-.101l-.004-5.502a.75.75 0 0 1 .75-.75m.005-3.497a.999.999 0 1 1 0 1.997a.999.999 0 0 1 0-1.997" />
</svg>',
        ],
        [
            'label' => 'Payment',
            'url' => '#',
            'mainmenu' => ['staff_setting'],
            'activeurls' => ['process-timesheet', 'staff-invoice'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M14.356 2.595a.25.25 0 0 1 .361-.032l.922.812L12.739 7h1.92l2.106-2.632l1.652 1.457a.25.25 0 0 1 .026.348l-.69.827h1.944a1.75 1.75 0 0 0-.288-2.3l-3.7-3.263a1.75 1.75 0 0 0-2.531.23L8.976 7h1.91zM16.25 14a.75.75 0 0 0 0 1.5h2a.75.75 0 0 0 0-1.5zM4.5 7.25a.75.75 0 0 1 .75-.75h3.128L9.57 5H5.25A2.25 2.25 0 0 0 3 7.25v10.5A3.25 3.25 0 0 0 6.25 21h12a3.25 3.25 0 0 0 3.25-3.25v-6.5A3.25 3.25 0 0 0 18.25 8h-13a.75.75 0 0 1-.75-.75m0 10.5V9.372q.354.126.75.128h13c.966 0 1.75.784 1.75 1.75v6.5a1.75 1.75 0 0 1-1.75 1.75h-12a1.75 1.75 0 0 1-1.75-1.75" />
</svg>',

            'sub_menu' => [
                [
                    'label' => 'Process Timesheet',
                    'url' => '/process-timesheet',
                ],
                [
                    'label' => 'View Timesheet Invoice',
                    'url' => '/staff-invoice/0/0',
                ],
            ],
        ],

        [
            'label' => 'Manage User Account',
            'url' => '/manage-user-account',
            'mainmenu' => ['users_setting'],
            'activeurls' => ['manage-user-account'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M17.755 14a2.25 2.25 0 0 1 2.248 2.25v.575c0 .894-.32 1.759-.9 2.438c-1.57 1.833-3.957 2.738-7.103 2.738s-5.532-.905-7.098-2.74a3.75 3.75 0 0 1-.898-2.434v-.578A2.25 2.25 0 0 1 6.253 14zm0 1.5H6.252a.75.75 0 0 0-.75.75v.577c0 .535.192 1.053.54 1.46c1.253 1.469 3.22 2.214 5.957 2.214c2.739 0 4.706-.745 5.963-2.213a2.25 2.25 0 0 0 .54-1.463v-.576a.75.75 0 0 0-.748-.749M12 2.005a5 5 0 1 1 0 10a5 5 0 0 1 0-10m0 1.5a3.5 3.5 0 1 0 0 7a3.5 3.5 0 0 0 0-7" />
</svg>',
        ],
        [
            'label' => 'Students User Account Management',
            'url' => '/bulk-user-management',
            'mainmenu' => ['users_setting'],
            'activeurls' => ['bulk-user-management'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>',
        ],
        [
            'label' => 'Create Students User Account',
            'url' => '/bulk-create-student-users',
            'mainmenu' => ['users_setting'],
            'activeurls' => ['bulk-create-student-users'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M19.5 6.25v5.063a6.5 6.5 0 0 1 1.5.709V6.25A3.25 3.25 0 0 0 17.75 3H6.25A3.25 3.25 0 0 0 3 6.25v11.5A3.25 3.25 0 0 0 6.25 21h5.772a6.5 6.5 0 0 1-.709-1.5H6.25a1.75 1.75 0 0 1-1.75-1.75V6.25c0-.966.784-1.75 1.75-1.75h11.5c.966 0 1.75.784 1.75 1.75M8.5 12h5.534a6.5 6.5 0 0 0-3.02 5.93C8.474 17.556 7 15.755 7 14v-.5A1.5 1.5 0 0 1 8.5 12M12 5.5a2.75 2.75 0 1 1 0 5.5a2.75 2.75 0 0 1 0-5.5m11 12a5.5 5.5 0 1 0-11 0a5.5 5.5 0 0 0 11 0m-5 .5l.001 2.503a.5.5 0 1 1-1 0V18h-2.505a.5.5 0 0 1 0-1H17v-2.5a.5.5 0 1 1 1 0V17h2.497a.5.5 0 0 1 0 1z" />
</svg>',
        ],
        [
            'label' => 'IP Track',
            'url' => '/user-activity-track',
            'mainmenu' => ['users_setting'],
            'activeurls' => ['user-activity-track'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="1.5"
                                d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>',
        ],

        [
            'label' => 'Courses',
            'url' => '#',
            'mainmenu' => ['administration'],
            'activeurls' => [
                'courses',
                'add-courses',
                'course-structure',
                'edit-courses',
                'view-subject-list',
                'add-subject',
                'edit-subject',
                'edit-element-competency',
                'element-of-unit-competency',
                'edit-subject-module',
                'course-subject',
                'view-unit-module-list',
                'add-unit-module',
                'edit-unit-module',
                'list-course-template',
                'course-template-structure',
                'edit-course-template',
                'add-course-template',
                'edit-course-template',
                'courses-upfront-fee-list',
                'add-courses-upfront-fee',
                'view-promotion-price-list',
                'edit-promotion-price',
                'add-promotion-price',
                'view-elicos-discount-list',
                'add-elicos-discount',
                'edit-elicos-discount',
                'courses-intake-date-list',
                'add-courses-intake-date',
                'view-holiday-list',
                'add-holiday',
                'edit-holiday',
                'training-plan-template',
            ],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>',

            'sub_menu' => [
                [
                    'label' => 'Manage Courses',
                    'url' => '/courses',
                    'subactiveurls' => ['add-courses', 'edit-courses', 'course-structure', 'view-subject-list', 'edit-subject', 'view-unit-module-list', 'add-unit-module', 'edit-unit-module', 'course-subject'],
                ],
                //                [
                //                    'label' => 'Course Template',
                //                    'url' => '/list-course-template',
                //                    'subactiveurls' => ['add-course-template','edit-course-template','course-template-structure']
                //                ],
                //                [
                //                    'label' => 'Course Upfront fee',
                //                    'url' => '/courses-upfront-fee-list',
                //                    'subactiveurls' => ['add-courses-upfront-fee']
                //                ],
                //                [
                //                    'label' => 'Course Promotion',
                //                    'url' => '/view-promotion-price-list',
                //                    'subactiveurls' => ['add-promotion-price','edit-promotion-price']
                //                ],
                //                [
                //                    'label' => 'ELICOS Discount Week',
                //                    'url' => '/view-elicos-discount-list',
                //                    'subactiveurls' => ['add-elicos-discount','edit-elicos-discount']
                //                ],
                //                [
                //                    'label' => 'Intake Dates',
                //                    'url' => '/courses-intake-date-list',
                //                    'subactiveurls' => ['add-courses-intake-date']
                //                ],
                //                [
                //                    'label' => 'Public /College Holidays',
                //                    'url' => '/view-holiday-list',
                //                    'subactiveurls' => ['add-holiday','edit-holiday']
                //                ],
                [
                    'label' => 'Training Plan Template',
                    'url' => '/training-plan-template',
                ],
            ],
        ],
        [
            'label' => 'Update Status',
            'url' => '#',
            'mainmenu' => ['administration'],
            'activeurls' => [
                'bulk-update-result',
                'update-unit-outcome',
                'update-student-unit-result',
                'update-student-result',
                'update-student-activity',
                'bulk-completion-update',
                'bulk-update-student-course',
                'bulk-update-course-template',
            ],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="1.5"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>',
            'sub_menu' => [
                //                [
                //                    'label' => 'Update Final Outcome',
                //                    'url' => '/bulk-update-result',
                //                ],
                //                [
                //                    'label' => 'Update Student Course Status',
                //                    'url' => '/bulk-completion-update',
                //                ],
                [
                    'label' => 'Update Student Course',
                    'url' => '/bulk-update-student-course',
                ],
                [
                    'label' => 'Update Student Course Beta',
                    'url' => '/spa/bulk-update-student-course',
                ],
                [
                    'label' => 'Update Student Course Template',
                    'url' => '/bulk-update-course-template',
                ],
                [
                    'label' => 'Update Student Course Template Beta',
                    'url' => '/spa/bulk-update-course-template',
                ],
            ],
        ],
        [
            'label' => 'Data Reporting',
            'url' => '#',
            'mainmenu' => ['administration'],
            'activeurls' => [
                'prisms-data-validation',
                'student-avet-miss-data-exports',
                'nsw-avetmiss-attpv2',
                'nsw-avetmiss-attpv1',
                'vsn',
                'nvr-report-data-extraction',
                'cqr-report-data-extraction',
                'vet-fee-help',
                'tcsi-report',
                'student-vet-fee-help-add',
            ],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>',
            'sub_menu' => [
                [
                    'label' => 'Validate PRISMS',
                    'url' => '/prisms-data-validation',
                ],
                [
                    'label' => 'Validate PRISMS Beta',
                    'url' => '/spa/prisms-data-validation',
                ],
                [
                    'label' => 'AVETMISS',
                    'url' => '/student-avet-miss-data-exports',
                ],
                [
                    'label' => 'AVETMISS Beta',
                    'url' => '/spa/student-avet-miss-data-exports',
                ],
                //                [
                //                    'label' => 'VSN',
                //                    'url' => '#',
                //                ],
                [
                    'label' => 'NVR',
                    'url' => '/nvr-report-data-extraction',
                ],
                [
                    'label' => 'NVR Beta',
                    'url' => '/spa/nvr-report-data-extraction',
                ],
                [
                    'label' => 'CQR',
                    'url' => '/cqr-report-data-extraction',
                ],
                [
                    'label' => 'CQR Beta',
                    'url' => '/spa/cqr-report-data-extraction',
                ],
                [
                    'label' => 'VET FEE HELP',
                    'url' => '/vet-fee-help',
                ],
                //                [
                //                    'label' => 'TCSI',
                //                    'url' => '/tcsi-report',
                //                ]
            ],
        ],
        [
            'label' => 'Survey Management',
            'url' => '#',
            'mainmenu' => ['administration'],
            'activeurls' => [
                'survey-manager-question-management',
                'survay-manager-send-request',
                'survey-manager-question-management-edit',
                'survey-manager-activation',
                'survay-student-question',
                'survay-student-submitter',
                'survey-result-not-send-request',
                'survey-result-send-request',
            ],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="1.5"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>',
            'sub_menu' => [
                [
                    'label' => 'Survey Management',
                    'url' => '/survey-manager-question-management',
                ],
                [
                    'label' => 'Survey Management Beta',
                    'url' => '/spa/survey-manager-question-management',
                ],
                [
                    'label' => 'Activation',
                    'url' => '/survey-manager-activation',
                ],
                [
                    'label' => 'Activation Beta',
                    'url' => '/spa/survey-manager-activation',
                ],
                [
                    'label' => 'Results (Not Send Request)',
                    'url' => '/survey-result-not-send-request',
                ],
                [
                    'label' => 'Results (Not Send Request) Beta',
                    'url' => '/spa/survey-result-not-send-request',
                ],
                [
                    'label' => 'Results (Send Request)',
                    'url' => '/survey-result-send-request',
                ],
                [
                    'label' => 'Results (Send Request) Beta',
                    'url' => '/spa/survey-result-send-request',
                ],
            ],
        ],
        //        [
        //            'label' => 'Templates',
        //            'url' => '#',
        //            'mainmenu' => ['administration'],
        //            'activeurls' => [
        //                'add-new-letter', 'edit-new-letter', 'report-letter-setup', 'view-email-template-list', 'add-email-template',
        //                'edit-email-template', 'view-sms-template-list', 'add-sms-template', 'edit-sms-template'
        //            ],
        //            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        //                        <path stroke-linecap="round"
        //                                stroke-linejoin="round"
        //                                stroke-width="1.5"
        //                                d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
        //                        </svg>',
        //            'sub_menu' => [
        //                [
        //                    'label' => 'Add New or Edit Letter',
        //                    'url' => '/report-letter-setup',
        //                    'subactiveurls' => ['add-new-letter','edit-new-letter']
        //                ],
        //                [
        //                    'label' => 'Add/Edit Email Template',
        //                    'url' => '/view-email-template-list',
        //                    'subactiveurls' => ['add-email-template','edit-email-template']
        //                ],
        //                [
        //                    'label' => 'Add/EditSMS Template',
        //                    'url' => '/view-sms-template-list',
        //                    'subactiveurls' => ['add-sms-template', 'edit-sms-template']
        //                ]
        //            ],
        //        ],
        [
            'label' => 'Letter Template',
            'url' => '#',
            'mainmenu' => ['administration'],
            'activeurls' => ['pdf-template-list', 'edit-pdf-template'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                        </svg>',
            'sub_menu' => [
                [
                    'label' => 'PDF Template List',
                    'url' => '/pdf-template-list',
                ],
                [
                    'label' => 'PDF Template List Beta',
                    'url' => '/spa/pdf-templates',
                ],
            ],
        ],
        //        [
        //            'label' => 'E-forms',
        //            'url' => '/elearning-link-list',
        //            'mainmenu' => ['administration'],
        //            'activeurls' => ['elearning-link-list'],
        //            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        //                       <path stroke-linecap="round"
        //                             stroke-linejoin="round"
        //                             stroke-width="1.5"
        //                             d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        //                        </svg>'
        //        ],
        //        [
        //            'label' => 'Reconcile Bank',
        //            'url' => '/bank-reconciliation',
        //            'mainmenu' => ['administration'],
        //            'activeurls' => ['bank-reconciliation'],
        //            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        //                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
        //                    </svg>'
        //        ],
        //        [
        //            'label' => 'Setup Account Payment',
        //            'url' => '/student-account-setup',
        //            'mainmenu' => ['administration'],
        //            'activeurls' => [
        //                'student-account-setup',
        //                'edit-payment-ledger',
        //                'student-ledger-account',
        //                'edit-ledger-account',
        //                'setup-staff-level',
        //                'define-level-activity',
        //                'payment-mode-info',
        //                'edit-payment-mode'
        //            ],
        //            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        //                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        //                    </svg>'
        //        ],
        [
            'label' => 'Manage Calendar',
            'url' => '#',
            'mainmenu' => ['administration'],
            'activeurls' => ['manage-semester', 'semester-division', 'course-calendar'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="1.5"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>',
            'sub_menu' => [
                [
                    'label' => 'Manage Semester',
                    'url' => '/manage-semester',
                ],

                [
                    'label' => 'Manage Semester Beta',
                    'url' => '/spa/manage-semester',
                ],

                [
                    'label' => 'Semester Division',
                    'url' => '/semester-division',
                ],

                [
                    'label' => 'Course Calendar Type',
                    'url' => '/course-calendar',
                ],

            ],
        ],
        [
            'label' => 'Manage Training Contracts',
            'url' => '#',
            'mainmenu' => ['administration'],
            'activeurls' => [
                'add-contract-code',
                'view-contract-code',
                'view-contract-funding-source',
                'add-contract-funding-source',
                'view-course-site',
                'add-course-site',
            ],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Add Contract',
                    'url' => '/add-contract-code',
                ],
                [
                    'label' => 'Add Contract Beta',
                    'url' => '/spa/add-contract-code',
                ],
                //                [
                //                    'label' => 'Manage Contract',
                //                    'url' => '/view-contract-code',
                //                ],
                //                [
                //                    'label' => 'Manage Contract Schedule',
                //                    'url' => '/view-contract-funding-source',
                //                ],
                //                [
                //                    'label' => 'Course Site',
                //                    'url' => '/view-course-site',
                //                ]
            ],
        ],
        //        [
        //            'label' => 'Manage Training Rooms',
        //            'url' => '#',
        //            'mainmenu' => ['administration'],
        //            'activeurls' => [
        //                'view-campus-list',
        //                'view-venue-list',
        //                'add-venue',
        //                'add-campus',
        //                'edit-venue',
        //                'edit-campus',
        //                'add-classroom',
        //                'edit-classroom',
        //                'view-classroom-list'
        //            ],
        //            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        //                            <path stroke-linecap="round"
        //                                    stroke-linejoin="round"
        //                                    stroke-width="1.5"
        //                                    d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
        //                            </svg>',
        //            'sub_menu' => [
        //                [
        //                    'label' => 'Campus',
        //                    'url' => '/view-campus-list',
        //                ],
        //                [
        //                    'label' => 'Venue',
        //                    'url' => '/view-venue-list',
        //                ],
        //                [
        //                    'label' => 'Training Location & Rooms',
        //                    'url' => '/view-classroom-list',
        //                ]
        //            ],
        //        ],
        //        [
        //            'label' => 'Manage Bank Information',
        //            'url' => '/bank-list',
        //            'mainmenu' => ['administration'],
        //            'activeurls' => ['bank-list'],
        //            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        //                        <path stroke-linecap="round"
        //                                stroke-linejoin="round"
        //                                stroke-width="1.5"
        //                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        //                        </svg>'
        //        ],
        //        [
        //            'label' => 'Reports',
        //            'url' => '/manage-reports',
        //            'mainmenu' => ['administration'],
        //            'activeurls' => ['manage-reports', 'generate-reports', 'generate-reports-letter'],
        //            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        //                        <path stroke-linecap="round"
        //                                stroke-linejoin="round"
        //                                stroke-width="1.5"
        //                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        //                        </svg>'
        //        ],
        //        [
        //            'label' => 'Failed Emails',
        //            'url' => '/failed-email',
        //            'mainmenu' => ['administration'],
        //            'activeurls' => ['failed-email'],
        //            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        //                            <path stroke-linecap="round"
        //                                    stroke-linejoin="round"
        //                                    stroke-width="1.5"
        //                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        //                            </svg>'
        //        ],
        [
            'label' => 'Edit My Profile',
            'url' => '/user_profile',
            'mainmenu' => ['administration'],
            'activeurls' => ['user_profile'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="1.5"
                                d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>',
        ],
        [
            'label' => 'Organisation Profile',
            'url' => '#',
            'mainmenu' => ['settings'],
            'activeurls' => ['general-info', 'training-organisation', 'main-location', 'bank-details', 'vsl-info', 'settings'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.5 5.5C7.94772 5.5 7.5 5.94772 7.5 6.5C7.5 7.05228 7.94772 7.5 8.5 7.5C9.05229 7.5 9.5 7.05228 9.5 6.5C9.5 5.94772 9.05229 5.5 8.5 5.5ZM7.5 13.5C7.5 12.9477 7.94772 12.5 8.5 12.5C9.05229 12.5 9.5 12.9477 9.5 13.5C9.5 14.0523 9.05229 14.5 8.5 14.5C7.94772 14.5 7.5 14.0523 7.5 13.5ZM8.5 9C7.94772 9 7.5 9.44771 7.5 10C7.5 10.5523 7.94772 11 8.5 11C9.05229 11 9.5 10.5523 9.5 10C9.5 9.44771 9.05229 9 8.5 9ZM11 6.5C11 5.94772 11.4477 5.5 12 5.5C12.5523 5.5 13 5.94772 13 6.5C13 7.05228 12.5523 7.5 12 7.5C11.4477 7.5 11 7.05228 11 6.5ZM12 12.5C11.4477 12.5 11 12.9477 11 13.5C11 14.0523 11.4477 14.5 12 14.5C12.5523 14.5 13 14.0523 13 13.5C13 12.9477 12.5523 12.5 12 12.5ZM14.5 13.5C14.5 12.9477 14.9477 12.5 15.5 12.5C16.0523 12.5 16.5 12.9477 16.5 13.5C16.5 14.0523 16.0523 14.5 15.5 14.5C14.9477 14.5 14.5 14.0523 14.5 13.5ZM12 9C11.4477 9 11 9.44771 11 10C11 10.5523 11.4477 11 12 11C12.5523 11 13 10.5523 13 10C13 9.44771 12.5523 9 12 9ZM6.25 2C5.00736 2 4 3.00736 4 4.25V20.75C4 21.1642 4.33579 21.5 4.75 21.5H19.2528C19.667 21.5 20.0028 21.1642 20.0028 20.75V11.7493C20.0028 10.5067 18.9954 9.4993 17.7528 9.4993H16.5V4.25C16.5 3.00736 15.4926 2 14.25 2H6.25ZM5.5 4.25C5.5 3.83579 5.83579 3.5 6.25 3.5H14.25C14.6642 3.5 15 3.83579 15 4.25V10.2493C15 10.6635 15.3358 10.9993 15.75 10.9993H17.7528C18.167 10.9993 18.5028 11.3351 18.5028 11.7493V20H16.5V17.25C16.5 16.8358 16.1642 16.5 15.75 16.5H8.25C7.83579 16.5 7.5 16.8358 7.5 17.25V20H5.5V4.25ZM15 18V20H12.75V18H15ZM11.25 18V20H9V18H11.25Z" fill="currentColor"/>
</svg>',
            'sub_menu' => [
                [
                    'label' => 'General Info',
                    'url' => '/general-info',
                ],
                [
                    'label' => 'Training Organisation Identifier',
                    'url' => '/training-organisation',
                ],
                [
                    'label' => 'Offer Main Location',
                    'url' => '/main-location',
                ],
                [
                    'label' => 'Bank Details',
                    'url' => '/bank-details',
                ],
                [
                    'label' => 'VET Study Loans (VSL) Info',
                    'url' => '/vsl-info',
                ],
            ],
        ],

        [
            'label' => 'Courses',
            'url' => '#',
            'mainmenu' => ['settings'],
            'activeurls' => ['spa.courses.index', 'spa.courses.profile', 'cvr-info', 'view-campus', 'course-types', 'courses-intake-date', 'course-template', 'view-elicos-discount', 'courses-upfront-fee', 'promotion-price'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M7 6a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1zm1.5 1.5h7v-1h-7zM4 4.5A2.5 2.5 0 0 1 6.5 2H18a2.5 2.5 0 0 1 2.5 2.5v14.25a.75.75 0 0 1-.75.75H5.5a1 1 0 0 0 1 1h13.25a.75.75 0 0 1 0 1.5H6.5A2.5 2.5 0 0 1 4 19.5zM5.5 18H19V4.5a1 1 0 0 0-1-1H6.5a1 1 0 0 0-1 1z" />
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Campus, Venues & Rooms ',
                    'url' => '/cvr-info',
                    'subactiveurls' => ['view-campus'],
                ],
                [
                    'label' => 'Course Type',
                    'url' => '/course-types',
                ],
                [
                    'label' => 'Manage Courses',
                    'url' => '/spa/courses',
                    'subactiveurls' => ['spa.courses.profile'],
                ],
                [
                    'label' => 'ELICOS Discount Week',
                    'url' => '/view-elicos-discount',
                ],
                [
                    'label' => 'Intake Dates',
                    'url' => '/courses-intake-date',
                ],
                [
                    'label' => 'Course Template',
                    'url' => '/course-template',
                ],
                [
                    'label' => 'Course Upfront fee',
                    'url' => '/courses-upfront-fee',
                ],
                [
                    'label' => 'Course Promotion',
                    'url' => '/promotion-price',
                ],
            ],
        ],
        //         [
        //             'label' => 'Courses Beta',
        //             'url' => '/spa/courses',
        //             'mainmenu' => ['settings'],
        //             'activeurls' => ['spa/courses'],
        //             'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
        // 	<path fill="currentColor" d="M7 6a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1zm1.5 1.5h7v-1h-7zM4 4.5A2.5 2.5 0 0 1 6.5 2H18a2.5 2.5 0 0 1 2.5 2.5v14.25a.75.75 0 0 1-.75.75H5.5a1 1 0 0 0 1 1h13.25a.75.75 0 0 1 0 1.5H6.5A2.5 2.5 0 0 1 4 19.5zM5.5 18H19V4.5a1 1 0 0 0-1-1H6.5a1 1 0 0 0-1 1z" />
        // </svg>',
        //         ],
        [
            'label' => 'Organization Operations',
            'url' => '#',
            'gap_after' => true,
            'mainmenu' => ['settings'],
            'activeurls' => ['intervention-strategy-view', 'manage-section', 'bulk-update-result', 'bulk-update-student-course', 'holiday-list', 'elearning-link-list'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M17.75 3A3.25 3.25 0 0 1 21 6.25v5.772a6.5 6.5 0 0 0-1.5-.709V8.5h-15v9.25c0 .966.784 1.75 1.75 1.75h5.063c.173.534.412 1.037.709 1.5H6.25A3.25 3.25 0 0 1 3 17.75V6.25A3.25 3.25 0 0 1 6.25 3zm0 1.5H6.25A1.75 1.75 0 0 0 4.5 6.25V7h15v-.75a1.75 1.75 0 0 0-1.75-1.75m-3.469 9.476a2 2 0 0 1-1.441 2.496l-.584.145a5.7 5.7 0 0 0 .006 1.807l.54.13a2 2 0 0 1 1.45 2.51l-.187.631c.44.386.94.7 1.484.922l.494-.519a2 2 0 0 1 2.899 0l.498.526a5.3 5.3 0 0 0 1.483-.913l-.198-.686a2 2 0 0 1 1.441-2.496l.584-.145a5.7 5.7 0 0 0-.006-1.807l-.54-.13a2 2 0 0 1-1.45-2.51l.187-.631a5.3 5.3 0 0 0-1.484-.922l-.493.518a2 2 0 0 1-2.9 0l-.498-.525c-.544.22-1.044.53-1.483.913zM17.503 19c-.8 0-1.45-.671-1.45-1.5c0-.828.65-1.5 1.45-1.5s1.45.672 1.45 1.5c0 .829-.65 1.5-1.45 1.5" />
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Intervention Strategy',
                    'url' => '/intervention-strategy-view',
                ],
                [
                    'label' => 'Section Setup',
                    'url' => '/manage-section',
                ],
                [
                    'label' => 'Update Final Outcome',
                    'url' => '/bulk-update-result',
                ],
                [
                    'label' => 'Update Student Course Status',
                    'url' => '/bulk-update-student-course',
                ],
                [
                    'label' => 'Public College Holidays',
                    'url' => '/holiday-list',
                ],
                [
                    'label' => 'Forms',
                    'url' => '/elearning-link-list',
                ],
            ],
        ],

        [
            'label' => 'Training',
            'url' => '#',
            'mainmenu' => ['settings'],
            'activeurls' => ['contract-code', 'contract-funding-source', 'course-site'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M10.25 3.5h3.5a.75.75 0 0 1 .75.75V6h-5V4.25a.75.75 0 0 1 .75-.75M8 4.25V6H6.25A3.25 3.25 0 0 0 3 9.25v7.5A3.25 3.25 0 0 0 6.25 20h11.5A3.25 3.25 0 0 0 21 16.75v-7.5A3.25 3.25 0 0 0 17.75 6H16V4.25A2.25 2.25 0 0 0 13.75 2h-3.5A2.25 2.25 0 0 0 8 4.25m11.5 5v1.5a1.75 1.75 0 0 1-1.75 1.75H14V12a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v.5H6.25a1.75 1.75 0 0 1-1.75-1.75v-1.5c0-.966.784-1.75 1.75-1.75h11.5c.966 0 1.75.784 1.75 1.75m0 4.24v3.26a1.75 1.75 0 0 1-1.75 1.75H6.25a1.75 1.75 0 0 1-1.75-1.75v-3.26c.505.322 1.106.51 1.75.51H10a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1h3.75c.644 0 1.245-.187 1.75-.51" />
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Manage Contract',
                    'url' => '/contract-code',
                ],
                [
                    'label' => 'Manage Contract Schedule',
                    'url' => '/contract-funding-source',
                ],
                [
                    'label' => 'View Course Site',
                    'url' => '/course-site',
                ],
            ],
        ],

        [
            'label' => 'Students',
            'url' => '#',
            'mainmenu' => ['settings'],
            'activeurls' => ['oshc-infos', 'added-services-fee-list', 'student-id-formate'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M19.75 4A2.25 2.25 0 0 1 22 6.25v11.505a2.25 2.25 0 0 1-2.25 2.25H4.25A2.25 2.25 0 0 1 2 17.755V6.25A2.25 2.25 0 0 1 4.25 4zm0 1.5H4.25a.75.75 0 0 0-.75.75v11.505c0 .414.336.75.75.75h15.5a.75.75 0 0 0 .75-.75V6.25a.75.75 0 0 0-.75-.75m-10 7a.75.75 0 0 1 .75.75v.493l-.008.108c-.163 1.113-1.094 1.65-2.492 1.65s-2.33-.537-2.492-1.65l-.008-.11v-.491a.75.75 0 0 1 .75-.75zm3.502.496h4.498a.75.75 0 0 1 .102 1.493l-.102.007h-4.498a.75.75 0 0 1-.102-1.493zh4.498zM8 8.502a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3m5.252.998h4.498a.75.75 0 0 1 .102 1.493L17.75 11h-4.498a.75.75 0 0 1-.102-1.493zh4.498z" />
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Overseas Student Health Coverage',
                    'url' => '/oshc-infos',
                ],
                [
                    'label' => 'Student Services Information',
                    'url' => '/added-services-fee-list',
                ],
                [
                    'label' => 'Student Id Format',
                    'url' => '/student-id-formate',
                ],
            ],
        ],

        [
            'label' => 'Agents',
            'url' => '#',
            'gap_after' => true,
            'mainmenu' => ['settings'],
            'activeurls' => ['agent-document', 'agent-status-view'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M8 6V4.25A2.25 2.25 0 0 1 10.25 2h3.5A2.25 2.25 0 0 1 16 4.25V6h1.75A3.25 3.25 0 0 1 21 9.25v5.25c0-1.264-.67-2.372-1.675-2.987c.112-.23.175-.49.175-.763v-1.5a1.75 1.75 0 0 0-1.75-1.75H6.25A1.75 1.75 0 0 0 4.5 9.25v1.5c0 .966.784 1.75 1.75 1.75H10V12a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v.5h.627a3.5 3.5 0 0 0-.592 1.5H14a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1H6.25a3.24 3.24 0 0 1-1.75-.51v3.26c0 .966.784 1.75 1.75 1.75h6.058A2.76 2.76 0 0 0 12 19.772v.103q0 .063.002.125H6.25A3.25 3.25 0 0 1 3 16.75v-7.5A3.25 3.25 0 0 1 6.25 6zm5.75-2.5h-3.5a.75.75 0 0 0-.75.75V6h5V4.25a.75.75 0 0 0-.75-.75m6.25 11a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0m2 5.375C22 21.431 20.714 23 17.5 23S13 21.437 13 19.875v-.103c0-.98.794-1.772 1.773-1.772h5.454c.98 0 1.773.793 1.773 1.772z" />
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Agent Document Checklist',
                    'url' => '/agent-document',
                ],
                [
                    'label' => 'Agent Status',
                    'url' => '/agent-status-view',
                ],
            ],
        ],

        [
            'label' => 'Applications',
            'url' => '#',
            'mainmenu' => ['settings'],
            'activeurls' => ['country-list', 'language-list'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M7 12.25a.75.75 0 1 1 1.5 0a.75.75 0 0 1-1.5 0m.75 2.25a.75.75 0 1 0 0 1.5a.75.75 0 0 0 0-1.5M7 18.25a.75.75 0 1 1 1.5 0a.75.75 0 0 1-1.5 0m3.75-6.75a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5zM10 15.25a.75.75 0 0 1 .75-.75h5.5a.75.75 0 0 1 0 1.5h-5.5a.75.75 0 0 1-.75-.75m.75 2.25a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5zm8.664-9.086l-5.829-5.828l-.049-.04l-.036-.03a2 2 0 0 0-.219-.18a1 1 0 0 0-.08-.044l-.048-.024l-.05-.029c-.054-.031-.109-.063-.166-.087a2 2 0 0 0-.624-.138q-.03-.002-.059-.007L12.172 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9.828a2 2 0 0 0-.586-1.414M18.5 20a.5.5 0 0 1-.5.5H6a.5.5 0 0 1-.5-.5V4a.5.5 0 0 1 .5-.5h6V8a2 2 0 0 0 2 2h4.5zm-5-15.379L17.378 8.5H14a.5.5 0 0 1-.5-.5z" />
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Country List',
                    'url' => '/country-list',
                ],
                [
                    'label' => 'Language List',
                    'url' => '/language-list',
                ],
            ],
        ],

        [
            'label' => 'Templates/Letters',
            'url' => '#',
            'mainmenu' => ['settings'],
            'activeurls' => ['letter-template-list', 'letter-setting-view', 'student-id-card-format', 'email-template-list', 'sms-template-list', 'checklist'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M8.379 5.5H6.25a.75.75 0 0 0-.75.75v13.5c0 .414.336.75.75.75h11.5a.75.75 0 0 0 .75-.75V6.25a.75.75 0 0 0-.75-.75h-2.129c-.404.603-1.09 1-1.871 1h-3.5c-.78 0-1.467-.397-1.871-1M15.986 4h1.764A2.25 2.25 0 0 1 20 6.25v13.5A2.25 2.25 0 0 1 17.75 22H6.25A2.25 2.25 0 0 1 4 19.75V6.25A2.25 2.25 0 0 1 6.25 4h1.764a2.25 2.25 0 0 1 2.236-2h3.5a2.25 2.25 0 0 1 2.236 2M9.5 4.25c0 .414.336.75.75.75h3.5a.75.75 0 0 0 0-1.5h-3.5a.75.75 0 0 0-.75.75" />
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Add New or Edit Letter',
                    'url' => '/letter-template-list',
                ],
                [
                    'label' => 'Add/Edit Email Template',
                    'url' => '/email-template-list',
                ],
                [
                    'label' => 'Add/Edit SMS Template',
                    'url' => '/sms-template-list',
                ],
                [
                    'label' => 'Checklist',
                    'url' => '/checklist',
                ],
                [
                    'label' => 'Letter Setting',
                    'url' => '/letter-setting-view',
                ],
                [
                    'label' => 'Student ID Card Setting',
                    'url' => '/student-id-card-format',
                ],
                [
                    'label' => 'Failed Emails',
                    'url' => '/failed-email',
                ],
            ],
        ],

        [
            'label' => 'Finance',
            'url' => '#',
            'mainmenu' => ['settings'],
            'activeurls' => ['enrollment-fees', 'bank-reconciliation', 'student-account-setup', 'bank-list', 'invoice-settings', 'spa.bank-reconciliation', 'spa.bulk-update-student-course', 'spa.student-account-setup'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M14.356 2.595a.25.25 0 0 1 .361-.032l.922.812L12.739 7h1.92l2.106-2.632l1.652 1.457a.25.25 0 0 1 .026.348l-.69.827h1.944a1.75 1.75 0 0 0-.288-2.3l-3.7-3.263a1.75 1.75 0 0 0-2.531.23L8.976 7h1.91zM16.25 14a.75.75 0 0 0 0 1.5h2a.75.75 0 0 0 0-1.5zM4.5 7.25a.75.75 0 0 1 .75-.75h3.128L9.57 5H5.25A2.25 2.25 0 0 0 3 7.25v10.5A3.25 3.25 0 0 0 6.25 21h12a3.25 3.25 0 0 0 3.25-3.25v-6.5A3.25 3.25 0 0 0 18.25 8h-13a.75.75 0 0 1-.75-.75m0 10.5V9.372q.354.126.75.128h13c.966 0 1.75.784 1.75 1.75v6.5a1.75 1.75 0 0 1-1.75 1.75h-12a1.75 1.75 0 0 1-1.75-1.75" />
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Enrollment Fee',
                    'url' => '/enrollment-fees',
                ],
                [
                    'label' => 'Reconcile Bank',
                    'url' => '/bank-reconciliation',
                ],
                [
                    'label' => 'Reconcile Bank Beta',
                    'url' => '/spa/bank-reconciliation',
                ],
                [
                    'label' => 'Setup Account Payment',
                    'url' => '/student-account-setup',
                ],
                [
                    'label' => 'Setup Account Payment Beta',
                    'url' => '/spa/student-account-setup',
                ],
                [
                    'label' => 'Manage Bank Information',
                    'url' => '/bank-list',
                ],
                [
                    'label' => 'Invoice Settings',
                    'url' => '/invoice-settings',
                ],
            ],
        ],

        [
            'label' => 'Data Reporting',
            'url' => '#',
            'gap_after' => true,
            'mainmenu' => ['settings'],
            'activeurls' => ['manage-report', 'report-letter-setup', 'prisms-data-validation', 'survey-manager-question-management', 'student-avet-miss-data-exports', 'nvr-report-data-extraction'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M13 10.75h6.715c.34-.347.78-.595 1.275-.698A9.5 9.5 0 0 0 11.5 1A9.5 9.5 0 0 0 2 10.5c0 5.079 3.986 9.227 9 9.487v-1.502a8 8 0 0 1 .25-15.981V9c0 .966.784 1.75 1.75 1.75m0-1.5a.25.25 0 0 1-.25-.25V2.597a8.01 8.01 0 0 1 6.653 6.653zM21.5 11a1.5 1.5 0 0 0-1.5 1.5v8a1.5 1.5 0 0 0 3 0v-8a1.5 1.5 0 0 0-1.5-1.5m-8 6a1.5 1.5 0 0 0-1.5 1.5v2a1.5 1.5 0 0 0 3 0v-2a1.5 1.5 0 0 0-1.5-1.5m2.5-1.5a1.5 1.5 0 0 1 3 0v5a1.5 1.5 0 0 1-3 0z" />
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Reports',
                    'url' => '/manage-report',
                ],
            ],
        ],

        [
            'label' => 'Other',
            'url' => '#',
            'mainmenu' => ['settings'],
            'activeurls' => [
                'competency-result-grade',
                'smtp-setup',
                'failed-jobs',
                'placement-provider',
                'certificate-id-format-view',
                'assessment-date-extension',
                'agent-email-template-setting-view',
                'offer-labels',
                'global-queue',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7 20L11 4M13 20L17 4M6 9H20M4 15H18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Competency - Grade',
                    'url' => '/competency-result-grade',
                ],
                [
                    'label' => 'SMTP Setup',
                    'url' => '/smtp-setup',
                ],
                [
                    'label' => 'Failed Jobs',
                    'url' => '/failed-jobs',
                ],
                [
                    'label' => 'Placement Provider',
                    'url' => '/placement-provider',
                ],
                [
                    'label' => 'Certificate Id Formate',
                    'url' => '/certificate-id-format-view',
                ],
                [
                    'label' => 'Extend Assessment Due Date',
                    'url' => '/assessment-date-extension',
                ],
                [
                    'label' => 'Notification Setting',
                    'url' => '/agent-email-template-setting-view',
                ],
                [
                    'label' => 'Offer Label',
                    'url' => '/offer-labels',
                ],
                [
                    'label' => 'ASQA Audit Report',
                    'url' => '/audit-report',
                ],
                [
                    'label' => 'Global Queue',
                    'url' => '/global-queue',
                ],
            ],
        ],

        [
            'label' => 'Onboard Setup',
            'url' => '/user-onboard',
            'mainmenu' => ['onboard-setup'],
            'activeurls' => ['user-onboard', 'onboard-setup', 'view-campus'],
            'svgicon' => '<svg width="24" height="24"
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="none"
		xmlns="http://www.w3.org/2000/svg">
                <path d="M1 10L3 8M3 8L10 1L17 8M3 8V18C3 18.5523 3.44772 19 4 19H7M17 8L19 10M17 8V18C17 18.5523 16.5523 19 16 19H13M7 19C7.55228 19 8 18.5523 8 18V14C8 13.4477 8.44772 13 9 13H11C11.5523 13 12 13.4477 12 14V18C12 18.5523 12.4477 19 13 19M7 19H13"
		stroke="#91D5FF"
		stroke-width="2"
		stroke-linecap="round"
		stroke-linejoin="round"/>
                </svg> ',
        ],

        // Add more menu items and submenus as needed

    ];
}
