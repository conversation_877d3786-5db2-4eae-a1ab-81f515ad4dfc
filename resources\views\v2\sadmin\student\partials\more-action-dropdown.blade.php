<div class="relative">
    <button type="button"
        class="moreActionBtn btn-secondary md:btn-primary relative w-8 h-8 px-0 md:px-3 md:w-auto md:!h-[1.875rem]">
        <svg width="12" height="12" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M1.5 0C0.671573 0 0 0.671573 0 1.5V3.5C0 4.32843 0.671573 5 1.5 5H3.5C4.32843 5 5 4.32843 5 3.5V1.5C5 0.671573 4.32843 0 3.5 0H1.5ZM1 1.5C1 1.22386 1.22386 1 1.5 1H3.5C3.77614 1 4 1.22386 4 1.5V3.5C4 3.77614 3.77614 4 3.5 4H1.5C1.22386 4 1 3.77614 1 3.5V1.5ZM8.5 0C7.67157 0 7 0.671573 7 1.5V3.5C7 4.32843 7.67157 5 8.5 5H10.5C11.3284 5 12 4.32843 12 3.5V1.5C12 0.671573 11.3284 0 10.5 0H8.5ZM8 1.5C8 1.22386 8.22386 1 8.5 1H10.5C10.7761 1 11 1.22386 11 1.5V3.5C11 3.77614 10.7761 4 10.5 4H8.5C8.22386 4 8 3.77614 8 3.5V1.5ZM0 8.5C0 7.67157 0.671573 7 1.5 7H3.5C4.32843 7 5 7.67157 5 8.5V10.5C5 11.3284 4.32843 12 3.5 12H1.5C0.671573 12 0 11.3284 0 10.5V8.5ZM1.5 8C1.22386 8 1 8.22386 1 8.5V10.5C1 10.7761 1.22386 11 1.5 11H3.5C3.77614 11 4 10.7761 4 10.5V8.5C4 8.22386 3.77614 8 3.5 8H1.5ZM8.5 7C7.67157 7 7 7.67157 7 8.5V10.5C7 11.3284 7.67157 12 8.5 12H10.5C11.3284 12 12 11.3284 12 10.5V8.5C12 7.67157 11.3284 7 10.5 7H8.5ZM8 8.5C8 8.22386 8.22386 8 8.5 8H10.5C10.7761 8 11 8.22386 11 8.5V10.5C11 10.7761 10.7761 11 10.5 11H8.5C8.22386 11 8 10.7761 8 10.5V8.5Z"
                fill="currentColor" />
        </svg>
        <p class="text-xs leading-none capitalize hidden md:block">More Actions</p>
    </button>
    <div class="absolute moreActionMenu md:!w-[600px] !w-96 hidden tw-popup tw-popup--tr">
        <div class="p-4 bg-white border rounded-md border-gray-200 w-full flex justify-between gap-4 shadow-popup">
            <div class="flex flex-col space-y-5 items-start justify-start w-full lg:hidden">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-gray-500 text-xs font-normal uppercase leading-none tracking-wider">
                        Mail Actions
                    </p>
                </div>
                <div class="flex-col items-start space-y-5 flex">
                    <button type="button" class="sendEmailBtn tw-icon-btn gap-1 p-0">
                        <span>
                            <svg width="16" height="16" viewBox="0 0 16 10" fill="none"
                                xmlns="http://www.w3.org/2000/svg" class="h-5 w-5">
                                <path
                                    d="M0 3.03807V8C0 9.10457 0.895431 10 2 10H10C11.1046 10 12 9.10457 12 8V2C12 0.895431 11.1046 0 10 0H2C0.895431 0 0 0.89543 0 2V3.03807C0 3.03809 0 3.03804 0 3.03807ZM2 1H10C10.5523 1 11 1.44772 11 2V2.73987L6.0001 5.43212L1 2.73976V2C1 1.44772 1.44772 1 2 1ZM1 3.87552L5.76305 6.44024C5.91104 6.51992 6.08916 6.51992 6.23715 6.44024L11 3.87562V8C11 8.55229 10.5523 9 10 9H2C1.44772 9 1 8.55228 1 8V3.87552Z"
                                    fill="#9CA3AF" />
                            </svg>
                        </span>
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Send Mail</p>
                    </button>
                    <button type="button" class="sendSmsBtn tw-icon-btn gap-1 p-0">
                        <span>
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                xmlns="http://www.w3.org/2000/svg" class="h-5 w-5">
                                <path
                                    d="M4 5C4 4.72386 4.22386 4.5 4.5 4.5H7.5C7.77614 4.5 8 4.72386 8 5C8 5.27614 7.77614 5.5 7.5 5.5H4.5C4.22386 5.5 4 5.27614 4 5ZM4.5 6.5C4.22386 6.5 4 6.72386 4 7C4 7.27614 4.22386 7.5 4.5 7.5H6.5C6.77614 7.5 7 7.27614 7 7C7 6.72386 6.77614 6.5 6.5 6.5H4.5ZM1.38797e-05 6C1.38797e-05 2.68629 2.68631 0 6.00001 0C9.31372 0 12 2.68629 12 6C12 9.31371 9.31372 12 6.00001 12C4.90639 12 3.87989 11.707 2.99618 11.195L0.658128 11.9743C0.482167 12.033 0.288196 11.9894 0.154224 11.8612C0.0202529 11.7329 -0.0317031 11.541 0.0192519 11.3626L0.730859 8.87202C0.264804 8.01862 1.38797e-05 7.03957 1.38797e-05 6ZM6.00001 1C3.23859 1 1.00001 3.23858 1.00001 6C1.00001 6.93308 1.25513 7.80506 1.69913 8.55165C1.76952 8.67002 1.78797 8.81217 1.75014 8.94458L1.24119 10.7259L2.89938 10.1732C3.04199 10.1256 3.19843 10.1448 3.32536 10.2253C4.09854 10.7159 5.01533 11 6.00001 11C8.76144 11 11 8.76142 11 6C11 3.23858 8.76144 1 6.00001 1Z"
                                    fill="#9CA3AF" />
                            </svg>
                        </span>
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Send SMS</p>
                    </button>
                    <button type="button" class="issueLetter tw-icon-btn gap-1 p-0">
                        <span>
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                xmlns="http://www.w3.org/2000/svg" class="h-5 w-5">
                                <path
                                    d="M2.08535 1C2.29127 0.417403 2.84689 0 3.5 0H6.5C7.15311 0 7.70873 0.417404 7.91465 1H8.5C9.32843 1 10 1.67157 10 2.5V6.21651L9.87005 5.91359C9.70318 5.52462 9.38412 5.23219 9 5.09372V2.5C9 2.22386 8.77614 2 8.5 2H7.91465C7.70873 2.5826 7.15311 3 6.5 3H3.5C2.84689 3 2.29127 2.5826 2.08535 2H1.5C1.22386 2 1 2.22386 1 2.5V12.5C1 12.7761 1.22386 13 1.5 13H4.08301C3.9633 13.3338 3.96951 13.6856 4.07953 14H1.5C0.671573 14 0 13.3284 0 12.5V2.5C0 1.67157 0.671573 1 1.5 1H2.08535ZM3.5 1C3.22386 1 3 1.22386 3 1.5C3 1.77614 3.22386 2 3.5 2H6.5C6.77614 2 7 1.77614 7 1.5C7 1.22386 6.77614 1 6.5 1H3.5ZM8.95128 6.30785C8.87242 6.12402 8.69159 6.00489 8.49157 6.00497C8.29154 6.00506 8.11082 6.12435 8.03211 6.30823L5.91655 11.2511C5.89725 11.2846 5.88173 11.3206 5.87057 11.3585L5.03607 13.3082C4.92741 13.5621 5.04513 13.856 5.29899 13.9646C5.55286 14.0733 5.84674 13.9556 5.9554 13.7017L6.68373 12H10.3051L11.0353 13.7021C11.1442 13.9559 11.4382 14.0733 11.6919 13.9645C11.9457 13.8556 12.0632 13.5616 11.9543 13.3078L11.1179 11.3581C11.1067 11.3202 11.0911 11.2842 11.0718 11.2507L8.95128 6.30785ZM9.8761 11H7.11174L8.49231 7.77444L9.8761 11Z"
                                    fill="#9CA3AF" />
                            </svg>
                        </span>
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Send Letter
                        </p>
                    </button>
                    <button type="button" class="quickAddNoteButton tw-icon-btn gap-1 p-0">
                        <svg width="16" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M9 4.5C9 6.98528 6.98528 9 4.5 9C2.01472 9 0 6.98528 0 4.5C0 2.01472 2.01472 0 4.5 0C6.98528 0 9 2.01472 9 4.5ZM5 2.5C5 2.22386 4.77614 2 4.5 2C4.22386 2 4 2.22386 4 2.5V4H2.5C2.22386 4 2 4.22386 2 4.5C2 4.77614 2.22386 5 2.5 5H4L4 6.5C4 6.77614 4.22386 7 4.5 7C4.77614 7 5 6.77614 5 6.5V5H6.5C6.77614 5 7 4.77614 7 4.5C7 4.22386 6.77614 4 6.5 4H5V2.5ZM11.5 3H9.79297C9.69436 2.65136 9.56223 2.31679 9.40029 2H11.5C12.8807 2 14 3.11929 14 4.5V8.78579C14 9.18361 13.842 9.56514 13.5607 9.84645L9.84645 13.5607C9.56514 13.842 9.18361 14 8.78579 14L4.5 14C3.11929 14 2 12.8807 2 11.5V9.40029C2.31679 9.56223 2.65136 9.69436 3 9.79297V11.5C3 12.3284 3.67157 13 4.5 13L8 13V11C8 9.34315 9.34315 8 11 8H13V4.5C13 3.67157 12.3284 3 11.5 3ZM9 12.9518C9.05125 12.9275 9.09847 12.8944 9.13934 12.8536L12.8536 9.13934C12.8944 9.09847 12.9275 9.05125 12.9518 9H11C9.89543 9 9 9.89543 9 11V12.9518Z"
                                fill="#9CA3AF" />
                        </svg>
                        <p class="text-sm leading-4 text-gray-900">Add Note</p>
                    </button>
                </div>
            </div>
            <div class="flex flex-col space-y-5 items-start justify-start min-w-fit">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-gray-500 text-xs font-normal uppercase leading-none tracking-wider">
                        Profile</p>
                </div>
                <button type="button" class="edit_profile inline-flex space-x-2 items-center justify-start ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M11.245 0.817547C12.3318 -0.26926 14.0938 -0.26926 15.1806 0.817547C16.2222 1.85907 16.2656 3.52075 15.3108 4.61395L15.1806 4.75322L5.57482 14.359C5.36973 14.5641 5.12299 14.7217 4.85212 14.8215L4.68684 14.8744L0.631867 15.9803C0.285278 16.0748 -0.0347856 15.7862 0.00276241 15.4457L0.0179263 15.3663L1.12383 11.3113C1.20014 11.0315 1.33621 10.7723 1.52188 10.5513L1.63914 10.4234L11.245 0.817547ZM10.3848 3.09244L2.34625 11.1305C2.25396 11.2228 2.18093 11.3319 2.13086 11.4516L2.08859 11.5745L1.21196 14.7852L4.42372 13.9096C4.50767 13.8867 4.5879 13.853 4.66253 13.8097L4.77003 13.7377L4.86771 13.6519L12.9058 5.61344L10.3848 3.09244ZM14.4735 1.52465C13.8159 0.867053 12.7725 0.83052 12.072 1.41505L11.9521 1.52465L11.0918 2.38544L13.6128 4.90644L14.4735 4.04612C15.1311 3.38852 15.1677 2.34503 14.5831 1.64456L14.4735 1.52465Z"
                            fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Edit User Details</p>
                    </div>
                </button>
                <button type="button" class="resetPasswordBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M13 4C13 4.55228 12.5523 5 12 5C11.4477 5 11 4.55228 11 4C11 3.44772 11.4477 3 12 3C12.5523 3 13 3.44772 13 4ZM10.5 0C7.42386 0 5 2.42386 5 5.5C5 5.89668 5.04001 6.29598 5.12154 6.67456C5.17968 6.94454 5.11378 7.17912 4.98018 7.31271L0.43934 11.8536C0.158035 12.1349 0 12.5164 0 12.9142V14.5C0 15.3284 0.671573 16 1.5 16H3.5C4.32843 16 5 15.3284 5 14.5V14H6C6.55228 14 7 13.5523 7 13V12H8C8.55229 12 9 11.5523 9 11V10.8199C9.4935 10.9536 10.0069 11 10.5 11C13.5761 11 16 8.57614 16 5.5C16 2.42386 13.5761 0 10.5 0ZM6 5.5C6 2.97614 7.97614 1 10.5 1C13.0239 1 15 2.97614 15 5.5C15 8.02386 13.0239 10 10.5 10C9.84083 10 9.22742 9.9047 8.72361 9.65279C8.56861 9.57529 8.38454 9.58357 8.23713 9.67467C8.08973 9.76578 8 9.92671 8 10.1V11H7C6.44772 11 6 11.4477 6 12V13H5C4.44772 13 4 13.4477 4 14V14.5C4 14.7761 3.77614 15 3.5 15H1.5C1.22386 15 1 14.7761 1 14.5V12.9142C1 12.7816 1.05268 12.6544 1.14645 12.5607L5.68729 8.01982C6.11942 7.58768 6.20942 6.97619 6.09913 6.46404C6.03354 6.15949 6 5.83105 6 5.5Z"
                            fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Reset password</p>
                    </div>
                </button>
                <button type="button"
                    class="reSendActivationBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M14.9343 7.04998C14.4096 3.21975 10.8793 0.540074 7.04906 1.06475C5.10631 1.33087 3.4602 2.36951 2.37695 3.82964L2.25058 3.99997H5.49997C5.77611 3.99997 5.99997 4.22383 5.99997 4.49997C5.99997 4.77611 5.77611 4.99997 5.49997 4.99997H1.49997C1.22383 4.99997 0.999972 4.77611 0.999972 4.49997V0.499969C0.999972 0.223826 1.22383 -3.14998e-05 1.49997 -3.14998e-05C1.77611 -3.14998e-05 1.99997 0.223826 1.99997 0.499968V2.70729C3.22428 1.31847 4.93475 0.345036 6.91335 0.0740017C11.2907 -0.525627 15.3254 2.53686 15.9251 6.91426C16.5247 11.2917 13.4622 15.3263 9.08479 15.926C4.7074 16.5256 0.672716 13.4631 0.073087 9.08571C-0.00105927 8.54443 -0.0192058 8.00788 0.0143779 7.48208C0.0319808 7.2065 0.269653 6.99737 0.545233 7.01497C0.820813 7.03258 1.02995 7.27025 1.01234 7.54583C0.982988 8.00544 0.998791 8.47516 1.06383 8.94999C1.58851 12.7802 5.11886 15.4599 8.94908 14.9352C12.7793 14.4105 15.459 10.8802 14.9343 7.04998Z"
                            fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1">
                        <p class="text-sm leading-4 text-gray-900">Re-send Activation</p>
                    </div>
                </button>
            </div>
            <div class="flex flex-col space-y-5 items-start justify-start min-w-fit">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-gray-500 text-xs font-normal uppercase leading-none tracking-wider">
                        Product</p>
                </div>
                <button type="button"
                    class="studentInterventionBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M7 4C7.27614 4 7.5 4.22386 7.5 4.5V9.5C7.5 9.77614 7.27614 10 7 10C6.72386 10 6.5 9.77614 6.5 9.5V4.5C6.5 4.22386 6.72386 4 7 4ZM7 12.5C7.41421 12.5 7.75 12.1642 7.75 11.75C7.75 11.3358 7.41421 11 7 11C6.58579 11 6.25 11.3358 6.25 11.75C6.25 12.1642 6.58579 12.5 7 12.5ZM6.72265 0.0839749C6.8906 -0.0279916 7.1094 -0.0279916 7.27735 0.0839749C9.21554 1.3761 11.3117 2.1823 13.5707 2.50503C13.817 2.54021 14 2.75117 14 3V7.5C14 11.3913 11.693 14.2307 7.17949 15.9667C7.06396 16.0111 6.93604 16.0111 6.82051 15.9667C2.30699 14.2307 0 11.3913 0 7.5V3C0 2.75117 0.182965 2.54021 0.429289 2.50503C2.68833 2.1823 4.78446 1.3761 6.72265 0.0839749ZM6.59914 1.34583C4.85275 2.39606 2.98541 3.09055 1 3.42787V7.5C1 10.892 2.96795 13.3634 7 14.9632C11.0321 13.3634 13 10.892 13 7.5V3.42787C11.0146 3.09055 9.14725 2.39606 7.40086 1.34583L7 1.09715L6.59914 1.34583Z"
                            fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Intervention</p>
                    </div>
                </button>
                <button type="button" class="gteDashboardBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="12" height="16" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M5 6.5C5 6.22386 5.22386 6 5.5 6H9C9.27614 6 9.5 6.22386 9.5 6.5C9.5 6.77614 9.27614 7 9 7H5.5C5.22386 7 5 6.77614 5 6.5ZM5 9.5C5 9.22386 5.22386 9 5.5 9H9C9.27614 9 9.5 9.22386 9.5 9.5C9.5 9.77614 9.27614 10 9 10H5.5C5.22386 10 5 9.77614 5 9.5ZM5 12.5C5 12.2239 5.22386 12 5.5 12H9C9.27614 12 9.5 12.2239 9.5 12.5C9.5 12.7761 9.27614 13 9 13H5.5C5.22386 13 5 12.7761 5 12.5ZM4 6.5C4 6.91421 3.66421 7.25 3.25 7.25C2.83579 7.25 2.5 6.91421 2.5 6.5C2.5 6.08579 2.83579 5.75 3.25 5.75C3.66421 5.75 4 6.08579 4 6.5ZM4 9.5C4 9.91421 3.66421 10.25 3.25 10.25C2.83579 10.25 2.5 9.91421 2.5 9.5C2.5 9.08579 2.83579 8.75 3.25 8.75C3.66421 8.75 4 9.08579 4 9.5ZM3.25 13.25C3.66421 13.25 4 12.9142 4 12.5C4 12.0858 3.66421 11.75 3.25 11.75C2.83579 11.75 2.5 12.0858 2.5 12.5C2.5 12.9142 2.83579 13.25 3.25 13.25ZM3.08535 1C3.29127 0.417403 3.84689 0 4.5 0H7.5C8.15311 0 8.70873 0.417404 8.91465 1H10.5C11.3284 1 12 1.67157 12 2.5V14.5C12 15.3284 11.3284 16 10.5 16H1.5C0.671573 16 0 15.3284 0 14.5V2.5C0 1.67157 0.671573 1 1.5 1H3.08535ZM4.5 1C4.22386 1 4 1.22386 4 1.5C4 1.77614 4.22386 2 4.5 2H7.5C7.77614 2 8 1.77614 8 1.5C8 1.22386 7.77614 1 7.5 1H4.5ZM3.08535 2H1.5C1.22386 2 1 2.22386 1 2.5V14.5C1 14.7761 1.22386 15 1.5 15H10.5C10.7761 15 11 14.7761 11 14.5V2.5C11 2.22386 10.7761 2 10.5 2H8.91465C8.70873 2.5826 8.15311 3 7.5 3H4.5C3.84689 3 3.29127 2.5826 3.08535 2Z"
                            fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Genuine Student (GS) Requirement
                        </p>
                    </div>
                </button>
                <button type="button" class="studentCardPdfBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M6 4.5C6 5.32843 5.32843 6 4.5 6C3.67157 6 3 5.32843 3 4.5C3 3.67157 3.67157 3 4.5 3C5.32843 3 6 3.67157 6 4.5ZM2 7.69879C2 7.17479 2.42479 6.75 2.94879 6.75H6.05121C6.57521 6.75 7 7.17479 7 7.69879C7 8.54603 6.42338 9.28454 5.60144 9.49003L5.54243 9.50478C4.85801 9.67589 4.14199 9.67589 3.45757 9.50478L3.39856 9.49003C2.57661 9.28454 2 8.54603 2 7.69879ZM9.5 4C9.22386 4 9 4.22386 9 4.5C9 4.77614 9.22386 5 9.5 5H12.5C12.7761 5 13 4.77614 13 4.5C13 4.22386 12.7761 4 12.5 4H9.5ZM9.5 7C9.22386 7 9 7.22386 9 7.5C9 7.77614 9.22386 8 9.5 8H12.5C12.7761 8 13 7.77614 13 7.5C13 7.22386 12.7761 7 12.5 7H9.5ZM0 1.75C0 0.783502 0.783502 0 1.75 0H14.25C15.2165 0 16 0.783502 16 1.75V10.25C16 11.2165 15.2165 12 14.25 12H1.75C0.783501 12 0 11.2165 0 10.25V1.75ZM1.75 1C1.33579 1 1 1.33579 1 1.75V10.25C1 10.6642 1.33579 11 1.75 11H14.25C14.6642 11 15 10.6642 15 10.25V1.75C15 1.33579 14.6642 1 14.25 1H1.75Z"
                            fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Student Card</p>
                    </div>
                </button>
                <button type="button" class="studentCardPdfBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 4.5C6 5.32843 5.32843 6 4.5 6C3.67157 6 3 5.32843 3 4.5C3 3.67157 3.67157 3 4.5 3C5.32843 3 6 3.67157 6 4.5ZM2 7.69879C2 7.17479 2.42479 6.75 2.94879 6.75H6.05121C6.57521 6.75 7 7.17479 7 7.69879C7 8.54603 6.42338 9.28454 5.60144 9.49003L5.54243 9.50478C4.85801 9.67589 4.14199 9.67589 3.45757 9.50478L3.39856 9.49003C2.57661 9.28454 2 8.54603 2 7.69879ZM9.5 4C9.22386 4 9 4.22386 9 4.5C9 4.77614 9.22386 5 9.5 5H12.5C12.7761 5 13 4.77614 13 4.5C13 4.22386 12.7761 4 12.5 4H9.5ZM9.5 7C9.22386 7 9 7.22386 9 7.5C9 7.77614 9.22386 8 9.5 8H12.5C12.7761 8 13 7.77614 13 7.5C13 7.22386 12.7761 7 12.5 7H9.5ZM0 1.75C0 0.783502 0.783502 0 1.75 0H14.25C15.2165 0 16 0.783502 16 1.75V10.25C16 11.2165 15.2165 12 14.25 12H1.75C0.783501 12 0 11.2165 0 10.25V1.75ZM1.75 1C1.33579 1 1 1.33579 1 1.75V10.25C1 10.6642 1.33579 11 1.75 11H14.25C14.6642 11 15 10.6642 15 10.25V1.75C15 1.33579 14.6642 1 14.25 1H1.75Z" fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">Student Card V2</p>
                    </div>
                </button>
                <button type="button"
                    class="offerServiceAddedInfoBtn  inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M8.5 6.5C8.5 6.22386 8.27614 6 8 6C7.72386 6 7.5 6.22386 7.5 6.5V8.5H5.5C5.22386 8.5 5 8.72386 5 9C5 9.27614 5.22386 9.5 5.5 9.5H7.5V11.5C7.5 11.7761 7.72386 12 8 12C8.27614 12 8.5 11.7761 8.5 11.5V9.5H10.5C10.7761 9.5 11 9.27614 11 9C11 8.72386 10.7761 8.5 10.5 8.5H8.5V6.5ZM5 0.5C5 0.223858 5.22386 0 5.5 0H10.5C10.7761 0 11 0.223858 11 0.5V3H13C14.6569 3 16 4.34315 16 6V12C16 13.6569 14.6569 15 13 15H3C1.34315 15 0 13.6569 0 12V6C0 4.34315 1.34315 3 3 3H5V0.5ZM10 1H6V3H10V1ZM3 4C1.89543 4 1 4.89543 1 6V12C1 13.1046 1.89543 14 3 14H13C14.1046 14 15 13.1046 15 12V6C15 4.89543 14.1046 4 13 4H3Z"
                            fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            OSHC</p>
                    </div>
                </button>
                <button type="button" class="trainingPlanBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M14 2.5C14 1.11929 12.8807 0 11.5 0H2.5C1.11929 0 0 1.11929 0 2.5V11.5C0 12.8807 1.11929 14 2.5 14H6.59971C6.43777 13.6832 6.30564 13.3486 6.20703 13H2.5C1.67157 13 1 12.3284 1 11.5V4H13V6.20703C13.3486 6.30564 13.6832 6.43777 14 6.59971V2.5ZM2.5 1H11.5C12.3284 1 13 1.67157 13 2.5V3H1V2.5C1 1.67157 1.67157 1 2.5 1ZM11.5 16C13.9853 16 16 13.9853 16 11.5C16 9.01472 13.9853 7 11.5 7C9.01472 7 7 9.01472 7 11.5C7 13.9853 9.01472 16 11.5 16ZM11 9.5C11 9.22386 11.2239 9 11.5 9C11.7761 9 12 9.22386 12 9.5V11H13C13.2761 11 13.5 11.2239 13.5 11.5C13.5 11.7761 13.2761 12 13 12H11.5C11.2239 12 11 11.7761 11 11.5V9.5Z"
                            fill="#9CA3AF" />
                    </svg>
                    <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                        Training Plan</p>
                </button>
                <button type="button"
                    class="viewStudentTCSIBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="12" height="16" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M2.5 4C2.22386 4 2 4.22386 2 4.5C2 4.77614 2.22386 5 2.5 5H9.5C9.77614 5 10 4.77614 10 4.5C10 4.22386 9.77614 4 9.5 4H2.5ZM2 6.5C2 6.22386 2.22386 6 2.5 6H9.5C9.77614 6 10 6.22386 10 6.5C10 6.77614 9.77614 7 9.5 7H2.5C2.22386 7 2 6.77614 2 6.5ZM2.5 8C2.22386 8 2 8.22386 2 8.5C2 8.77614 2.22386 9 2.5 9H6.5C6.77614 9 7 8.77614 7 8.5C7 8.22386 6.77614 8 6.5 8H2.5ZM0 1.5C0 0.671573 0.671573 0 1.5 0H10.5C11.3284 0 12 0.671573 12 1.5V14.5C12 15.3284 11.3284 16 10.5 16H1.5C0.671573 16 0 15.3284 0 14.5V1.5ZM1.5 1C1.22386 1 1 1.22386 1 1.5V14.5C1 14.7761 1.22386 15 1.5 15H10.5C10.7761 15 11 14.7761 11 14.5V1.5C11 1.22386 10.7761 1 10.5 1H1.5Z"
                            fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            TCSI</p>
                    </div>
                </button>
            </div>
            <div class="flex flex-col space-y-5 items-start justify-start min-w-fit">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-gray-500 text-xs font-normal uppercase leading-none tracking-wider">
                        Remove</p>
                </div>
                <button type="button"
                    class="manageCourseVariantBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.00237 0.483519C8.02038 0.759074 7.8116 0.997056 7.53604 1.01507C3.88659 1.25361 1 4.28989 1 7.99995C1 11.8659 4.13401 15 8 15C11.7105 15 14.747 12.1127 14.985 8.46273C15.0029 8.18717 15.2409 7.97835 15.5164 7.99631C15.792 8.01428 16.0008 8.25222 15.9828 8.52778C15.7108 12.7002 12.241 16 8 16C3.58172 16 0 12.4182 0 7.99995C0 3.75946 3.29899 0.289884 7.47082 0.0171963C7.74637 -0.000814969 7.98436 0.207965 8.00237 0.483519ZM9.00712 0.550235C9.06195 0.279591 9.3258 0.104638 9.59645 0.159467C10.1561 0.27285 10.6946 0.444644 11.2053 0.668188C11.4583 0.778925 11.5736 1.07377 11.4628 1.32673C11.3521 1.5797 11.0573 1.695 10.8043 1.58426C10.3579 1.38884 9.88716 1.23868 9.39789 1.13956C9.12725 1.08473 8.95229 0.820879 9.00712 0.550235ZM15.3358 4.80347C15.2254 4.55037 14.9307 4.43471 14.6776 4.54514C14.4245 4.65557 14.3089 4.95027 14.4193 5.20337C14.6128 5.64691 14.7617 6.11436 14.8602 6.60011C14.9151 6.87074 15.179 7.04562 15.4497 6.99071C15.7203 6.93581 15.8952 6.67191 15.8403 6.40128C15.7275 5.84563 15.5572 5.31083 15.3358 4.80347ZM12.4148 1.91597C12.5916 1.70385 12.9068 1.67521 13.119 1.85202C13.5074 2.1758 13.8647 2.53571 14.1857 2.92661C14.3609 3.14003 14.33 3.45509 14.1165 3.63033C13.9031 3.80556 13.588 3.7746 13.4128 3.56118C13.1318 3.21889 12.8189 2.90371 12.4787 2.62018C12.2666 2.44338 12.238 2.12809 12.4148 1.91597ZM8 3.5C8 3.22386 7.77614 3 7.5 3C7.22386 3 7 3.22386 7 3.5V8.5C7 8.77614 7.22386 9 7.5 9H10.5C10.7761 9 11 8.77614 11 8.5C11 8.22386 10.7761 8 10.5 8H8V3.5Z" fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">Defer Student</p>
                    </div>
                </button>
                <button type="button" class="viewSanctionBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8ZM15 8C15 6.24696 14.3556 4.64442 13.2907 3.41636L3.41636 13.2907C4.64442 14.3556 6.24696 15 8 15C11.866 15 15 11.866 15 8ZM2.70925 12.5836L12.5836 2.70925C11.3556 1.6444 9.75303 1 8 1C4.13401 1 1 4.13401 1 8C1 9.75303 1.6444 11.3556 2.70925 12.5836Z"
                            fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Sanction</p>
                    </div>
                </button>
                <button type="button"
                    class="viewExitInterViewBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M7.49279 3.90114C7.13479 3.9711 6.7439 4.20337 6.43474 4.74712C6.29826 4.98718 5.99301 5.07114 5.75296 4.93465C5.5129 4.79817 5.42894 4.49292 5.56543 4.25287C6.00628 3.47749 6.63328 3.0502 7.30099 2.9197C7.9539 2.7921 8.60295 2.95667 9.09316 3.28126C9.57681 3.6015 9.96793 4.12423 9.98039 4.75187C9.99339 5.40602 9.59456 5.99672 8.86123 6.43036C8.36263 6.7252 8.16869 6.92513 8.08626 7.05795C8.01687 7.16975 8.00009 7.27172 8.00009 7.5C8.00009 7.77614 7.77623 8 7.50009 8C7.22394 8 7.00009 7.77614 7.00009 7.5C7.00009 7.22827 7.01399 6.88929 7.2366 6.53061C7.44617 6.19295 7.80555 5.89287 8.35222 5.5696C8.88718 5.25326 8.98416 4.95168 8.98059 4.77173C8.97649 4.56527 8.83866 4.31208 8.54108 4.11505C8.25005 3.92235 7.86559 3.82828 7.49279 3.90114ZM7.75 10.5C8.16421 10.5 8.5 10.1642 8.5 9.75C8.5 9.33579 8.16421 9 7.75 9C7.33579 9 7 9.33579 7 9.75C7 10.1642 7.33579 10.5 7.75 10.5ZM7.50019 0C3.91034 0 1.00019 2.91015 1.00019 6.5C1.00019 7.651 1.29978 8.73335 1.82544 9.67194L1.0297 11.7542C0.736416 12.5216 1.4424 13.2957 2.23352 13.0741L4.7209 12.3774C5.5641 12.7768 6.50671 13 7.50019 13C11.09 13 14.0002 10.0899 14.0002 6.5C14.0002 2.91015 11.09 0 7.50019 0ZM2.00019 6.5C2.00019 3.46243 4.46263 1 7.50019 1C10.5378 1 13.0002 3.46243 13.0002 6.5C13.0002 9.53757 10.5378 12 7.50019 12C6.59806 12 5.74803 11.7832 4.99798 11.3993L4.82377 11.3101L1.96381 12.1111L2.93292 9.57525L2.80463 9.36552C2.29438 8.53135 2.00019 7.55079 2.00019 6.5ZM10.4628 16C8.49315 16 6.72814 15.1239 5.53613 13.7402C6.09918 13.8926 6.68851 13.981 7.29584 13.9973C8.19123 14.629 9.28368 15 10.4628 15C11.3649 15 12.2149 14.7832 12.965 14.3993L13.1392 14.3101L15.9992 15.1111L15.0301 12.5752L15.1583 12.3655C15.6686 11.5313 15.9628 10.5508 15.9628 9.49999C15.9628 8.34552 15.6071 7.27414 14.9993 6.38943C14.9905 5.78165 14.9095 5.19137 14.7643 4.6268C16.1125 5.81779 16.9628 7.5596 16.9628 9.49999C16.9628 10.651 16.6632 11.7333 16.1375 12.6719L16.9333 14.7542C17.2266 15.5216 16.5206 16.2957 15.7295 16.0741L13.2421 15.3774C12.3989 15.7768 11.4563 16 10.4628 16Z"
                            fill="#9CA3AF" />
                    </svg>
                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Exit Interview</p>
                    </div>
                </button>
                <button type="button"
                    class="studentDocumentListBtn inline-flex space-x-2 items-center justify-start w-full">
                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M8.5 0C13.1944 0 17 3.80556 17 8.5C17 13.1944 13.1944 17 8.5 17C3.80556 17 0 13.1944 0 8.5C0 3.80556 3.80556 0 8.5 0ZM8.5 1.0625C4.39238 1.0625 1.0625 4.39238 1.0625 8.5C1.0625 12.6076 4.39238 15.9375 8.5 15.9375C12.6076 15.9375 15.9375 12.6076 15.9375 8.5C15.9375 4.39238 12.6076 1.0625 8.5 1.0625ZM12.0683 5.9993C12.2528 6.18375 12.2733 6.46999 12.1298 6.67707L12.0683 6.7507L7.8132 11.0058C7.62875 11.1903 7.34251 11.2108 7.13543 11.0673L7.0618 11.0058L4.9368 8.8808C4.7294 8.6734 4.7294 8.33701 4.9368 8.12951C5.12125 7.94506 5.40749 7.92466 5.61457 8.06809L5.6882 8.12951L7.4375 9.87913L11.317 5.9993C11.5245 5.7919 11.8609 5.7919 12.0683 5.9993Z"
                            fill="#9CA3AF" />
                    </svg>

                    <div class="inline-flex flex-col items-start justify-start flex-1 truncate">
                        <p class="text-sm leading-tight text-gray-700 font-normal tracking-tight truncate">
                            Student Documents</p>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>