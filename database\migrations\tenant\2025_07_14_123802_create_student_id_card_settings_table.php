<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_id_card_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('college_id');
            $table->string('card_template', 50)->default('churchill'); // churchill, default, custom
            $table->string('card_orientation', 20)->default('landscape'); // landscape, portrait
            $table->string('primary_color', 7)->default('#0161a3'); // hex color code
            $table->string('secondary_color', 7)->default('#ffffff'); // hex color code
            $table->string('logo_position', 20)->default('top-left'); // top-left, top-right, top-center, bottom-left, bottom-right
            $table->boolean('show_expiry_date')->default(true);
            $table->integer('expiry_years')->default(2); // card validity in years
            $table->string('background_image')->nullable(); // path to background image
            $table->text('custom_css')->nullable(); // custom CSS for advanced styling
            $table->json('field_settings')->nullable(); // JSON for field visibility and positioning
            $table->timestamps();

            $table->index('college_id');
            $table->unique('college_id'); // One setting per college
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_id_card_settings');
    }
};
