<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentIdCardSetting extends Model
{
    use HasFactory;

    protected $table = 'student_id_card_format';

    protected $fillable = [
        'college_id',
        'card_template',
        'card_orientation',
        'primary_color',
        'secondary_color',
        'logo_position',
        'show_expiry_date',
        'expiry_years',
        'background_image',
        'custom_css',
        'field_settings'
    ];

    protected $casts = [
        'show_expiry_date' => 'boolean',
        'field_settings' => 'array'
    ];

    /**
     * Get the setting for a specific college
     */
    public static function getForCollege($collegeId)
    {
        return self::where('college_id', $collegeId)->first();
    }

    /**
     * Create or update settings for a college
     */
    public static function updateForCollege($collegeId, $data)
    {
        return self::updateOrCreate(
            ['college_id' => $collegeId],
            $data
        );
    }

    /**
     * Get default settings
     */
    public static function getDefaults()
    {
        return [
            'card_template' => 'churchill',
            'card_orientation' => 'landscape',
            'primary_color' => '#0161a3',
            'secondary_color' => '#ffffff',
            'logo_position' => 'top-left',
            'show_expiry_date' => true,
            'expiry_years' => 2,
            'background_image' => null,
            'custom_css' => null,
            'field_settings' => [
                'show_student_number' => true,
                'show_dob' => true,
                'show_course' => true,
                'show_campus' => true,
                'show_issue_date' => true,
                'show_expiry_date' => true
            ]
        ];
    }
}
