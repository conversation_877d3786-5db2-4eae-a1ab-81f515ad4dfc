@extends('v2.sadmin.layouts.app')

@section('title', $title)
@section('keywords', $keywords)
@section('description', $description)

@section('content')
    <x-v2.templates.page-header title="Student ID Card Setting" />

    <div class="flex h-full flex-row p-6">
        <div class="w-full bg-white rounded-lg p-6 shadow">
            <input id="college_id" type="hidden" name="college_id" value="{{ isset($college_id) ? $college_id : '' }}" />
            <livewire:onboarding.progress form="templates_letters.student_id_card_setting" />
            <div class="h-full w-full rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <div class="w-full">
                    <form id="studentIdCardSettingForm">
                        <input type="hidden" name="tracking_form" value="templates_letters.student_id_card_setting" />
                    </form>
                </div>
            </div>
        </div>
    </div>

    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/sadmin/student-id-card-setting.js') }}"></script>
        <script src="{{ asset('plugins/ckeditor5/ckeditor.js') }}"></script>
    </x-slot>
@endsection
